<?php
include_once '../config/database.php';

// Insert data siswa contoh untuk setiap kelas
$siswa_data = [
    // KPP A
    ['KPP A', '<PERSON>'],
    ['KPP A', '<PERSON><PERSON>'],
    ['KPP A', '<PERSON><PERSON><PERSON>'],
    ['KPP A', '<PERSON><PERSON>'],
    ['KPP A', '<PERSON><PERSON>'],
    
    // KPP B
    ['KPP B', 'Fajar Nugroho'],
    ['KPP B', '<PERSON><PERSON>'],
    ['KPP B', '<PERSON><PERSON>'],
    ['KPP B', 'Indah Sari'],
    ['KPP B', '<PERSON><PERSON>'],
    
    // KPPC
    ['KPPC', '<PERSON><PERSON><PERSON>'],
    ['KPPC', '<PERSON><PERSON> Ha<PERSON>'],
    ['KPPC', '<PERSON> Anggraini'],
    ['KPPC', '<PERSON><PERSON>'],
    ['KPPC', '<PERSON><PERSON>'],
    
    // X-1
    ['X-1', '<PERSON><PERSON>'],
    ['X-1', '<PERSON><PERSON><PERSON>'],
    ['X-1', '<PERSON><PERSON> Nur<PERSON>za'],
    ['X-1', '<PERSON><PERSON><PERSON>'],
    ['X-1', '<PERSON><PERSON>lsum'],
    
    // X-2
    ['X-2', 'Vina Marwah'],
    ['X-2', 'Wawan Setiawan'],
    ['X-2', 'Xenia Putri'],
    ['X-2', 'Yoga Pratama'],
    ['X-2', 'Zahra Aulia'],
    
    // XI-1
    ['XI-1', 'Adi Prasetyo'],
    ['XI-1', 'Bella Kartika'],
    ['XI-1', 'Candra Wijaya'],
    ['XI-1', 'Dewi Lestari'],
    ['XI-1', 'Elang Satria'],
    
    // XI-2
    ['XI-2', 'Fina Rahmawati'],
    ['XI-2', 'Ghani Pradana'],
    ['XI-2', 'Hesti Putri'],
    ['XI-2', 'Irfan Maulana'],
    ['XI-2', 'Juli Astuti'],
    
    // XII-1
    ['XII-1', 'Karina Widya'],
    ['XII-1', 'Lutfi Rahman'],
    ['XII-1', 'Mira Andini'],
    ['XII-1', 'Nizar Alamsyah'],
    ['XII-1', 'Olivia Handayani'],
    
    // XII-2
    ['XII-2', 'Prasetya Nugraha'],
    ['XII-2', 'Queen Amalia'],
    ['XII-2', 'Rafi Ardiansyah'],
    ['XII-2', 'Salsabila Putri'],
    ['XII-2', 'Teguh Santoso'],
    
    // KPA
    ['KPA', 'Ulfa Rahmawati'],
    ['KPA', 'Vicky Pratama'],
    ['KPA', 'Winda Sari'],
    ['KPA', 'Yusuf Kurniawan'],
    ['KPA', 'Zara Putri']
];

try {
    // Insert data siswa
    foreach($siswa_data as $data) {
        $kelas = $data[0];
        $nama = $data[1];
        
        // Dapatkan id_kelas
        $stmt = $pdo->prepare("SELECT id_kelas FROM kelas WHERE nama_kelas = ?");
        $stmt->execute([$kelas]);
        $kelas_result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if($kelas_result) {
            $id_kelas = $kelas_result['id_kelas'];
            
            // Insert siswa
            $stmt = $pdo->prepare("INSERT INTO siswa (nama_siswa, id_kelas) VALUES (?, ?)");
            $stmt->execute([$nama, $id_kelas]);
        }
    }
    
    echo "<h3>Data Kelas dan Siswa berhasil diinput!</h3>";
    echo "<p>Total " . count($siswa_data) . " siswa telah ditambahkan.</p>";
    
} catch(PDOException $e) {
    echo "Error: " . $e->getMessage();
}

// Tampilkan semua data untuk verifikasi
echo "<h4>Daftar Kelas:</h4>";
$stmt = $pdo->query("SELECT * FROM kelas ORDER BY nama_kelas");
$kelas_list = $stmt->fetchAll(PDO::FETCH_ASSOC);
echo "<ul>";
foreach($kelas_list as $kelas) {
    echo "<li>" . $kelas['nama_kelas'] . "</li>";
}
echo "</ul>";

echo "<h4>Contoh Data Siswa:</h4>";
$stmt = $pdo->query("SELECT s.nama_siswa, k.nama_kelas FROM siswa s JOIN kelas k ON s.id_kelas = k.id_kelas ORDER BY k.nama_kelas, s.nama_siswa LIMIT 10");
$siswa_list = $stmt->fetchAll(PDO::FETCH_ASSOC);
echo "<ul>";
foreach($siswa_list as $siswa) {
    echo "<li>" . $siswa['nama_siswa'] . " - " . $siswa['nama_kelas'] . "</li>";
}
echo "</ul>";

echo "<p><a href='../index.php' class='btn btn-primary'>Kembali ke Aplikasi</a></p>";
?>