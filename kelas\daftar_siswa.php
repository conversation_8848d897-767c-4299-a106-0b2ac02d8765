<?php 
include_once '../includes/header.php';
include_once '../includes/functions.php';

// Handle delete
if(isset($_GET['hapus'])) {
    $id_siswa = $_GET['hapus'];
    try {
        $sql = "DELETE FROM siswa WHERE id_siswa = ?";
        $stmt = $pdo->prepare($sql);
        $stmt->execute([$id_siswa]);
        $success_message = "Siswa berhasil dihapus!";
    } catch(PDOException $e) {
        $error_message = "Error: " . $e->getMessage();
    }
}

// Get all students with class information
$sql = "SELECT s.*, k.nama_kelas 
        FROM siswa s 
        JOIN kelas k ON s.id_kelas = k.id_kelas 
        ORDER BY k.nama_kelas, s.nama_siswa";
$stmt = $pdo->query($sql);
$siswas = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Group students by class
$siswa_by_kelas = [];
foreach($siswas as $siswa) {
    $siswa_by_kelas[$siswa['nama_kelas']][] = $siswa;
}

// Hitung total siswa
$total_siswa = count($siswas);
?>

<h2>Daftar Siswa</h2>

<?php if(isset($success_message)): ?>
    <div class="alert alert-success"><?= $success_message ?></div>
<?php endif; ?>

<?php if(isset($error_message)): ?>
    <div class="alert alert-danger"><?= $error_message ?></div>
<?php endif; ?>

<div class="row mb-3">
    <div class="col-md-6">
        <div class="card">
            <div class="card-body">
                <h5>Total Siswa: <?= $total_siswa ?></h5>
                <p class="mb-0">Total Kelas: <?= count($siswa_by_kelas) ?></p>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="d-flex gap-2">
            <a href="tambah_siswa.php" class="btn btn-primary">Tambah Siswa</a>
            <a href="import_siswa.php" class="btn btn-success">Import Siswa</a>
            <a href="export_siswa.php" class="btn btn-warning">Export Data</a>
        </div>
    </div>
</div>

<div class="mb-3">
    <a href="../index.php" class="btn btn-outline-secondary">Kembali</a>
</div>

<div class="accordion" id="siswaAccordion">
    <?php $no_kelas = 1; foreach($siswa_by_kelas as $kelas => $siswa_list): ?>
    <div class="accordion-item">
        <h2 class="accordion-header" id="heading<?= $no_kelas ?>">
            <button class="accordion-button <?= $no_kelas > 1 ? 'collapsed' : '' ?>" type="button" data-bs-toggle="collapse" 
                    data-bs-target="#collapse<?= $no_kelas ?>" aria-expanded="<?= $no_kelas == 1 ? 'true' : 'false' ?>" 
                    aria-controls="collapse<?= $no_kelas ?>">
                <?= $kelas ?> (<?= count($siswa_list) ?> siswa)
            </button>
        </h2>
        <div id="collapse<?= $no_kelas ?>" class="accordion-collapse collapse <?= $no_kelas == 1 ? 'show' : '' ?>" 
             aria-labelledby="heading<?= $no_kelas ?>" data-bs-parent="#siswaAccordion">
            <div class="accordion-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>No</th>
                                <th>Nama Siswa</th>
                                <th>Aksi</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $no = 1; foreach($siswa_list as $siswa): ?>
                            <tr>
                                <td><?= $no++ ?></td>
                                <td><?= $siswa['nama_siswa'] ?></td>
                                <td>
                                    <a href="edit_siswa.php?id=<?= $siswa['id_siswa'] ?>" 
                                       class="btn btn-sm btn-warning">Edit</a>
                                    <a href="?hapus=<?= $siswa['id_siswa'] ?>" 
                                       class="btn btn-sm btn-danger" 
                                       onclick="return confirm('Yakin ingin menghapus siswa <?= $siswa['nama_siswa'] ?>?')">Hapus</a>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    <?php $no_kelas++; endforeach; ?>
</div>

<?php include_once '../includes/footer.php'; ?>