# 📦 Sistem Peminjaman Barang & ATK

Aplikasi web modern untuk mengelola peminjaman barang dan pengambilan alat tulis kantor (ATK) di sekolah dengan interface yang responsif dan user-friendly.

## ✨ Fitur Utama

### 🎯 Peminjaman Barang
- ✅ Tambah, edit, dan hapus data peminjaman
- ✅ Tracking status peminjaman (dipinjam/dikembalikan)
- ✅ Deteksi otomatis keterlambatan pengembalian
- ✅ Laporan dan statistik real-time

### 📝 Pengambilan ATK
- ✅ Manajemen stok alat tulis kantor
- ✅ Tracking pengambilan per siswa/kelas
- ✅ Alert stok menipis
- ✅ Laporan penggunaan ATK

### 👥 Manajemen Siswa
- ✅ Data siswa per kelas
- ✅ Import/Export data CSV
- ✅ Manajemen kelas

### 📊 Dashboard & Laporan
- ✅ Statistik real-time
- ✅ Dashboard modern dengan charts
- ✅ Export laporan ke CSV
- ✅ Responsive design untuk semua device

## 🚀 Teknologi

- **Backend**: PHP 7.4+ dengan PDO
- **Database**: MySQL 5.7+
- **Frontend**: HTML5, CSS3, JavaScript ES6+
- **Framework**: Bootstrap 5.3.2
- **Icons**: Bootstrap Icons
- **Fonts**: Inter (Google Fonts)

## 📋 Persyaratan Sistem

- **Web Server**: Apache/Nginx
- **PHP**: 7.4 atau lebih baru
- **MySQL**: 5.7 atau lebih baru
- **Browser**: Chrome, Firefox, Safari, Edge (versi terbaru)

## 🛠️ Instalasi

### 1. Setup XAMPP/WAMP
```bash
# Download dan install XAMPP
# Pastikan Apache dan MySQL berjalan
```

### 2. Clone/Download Project
```bash
# Letakkan folder aplikasi di htdocs
C:\xampp\htdocs\aplikasi_peminjaman\
```

### 3. Setup Database
```sql
-- Buka phpMyAdmin (http://localhost/phpmyadmin)
-- Import file database_setup.sql
-- Atau jalankan query di database_setup.sql secara manual
```

### 4. Konfigurasi Database
```php
// Edit config/database.php jika diperlukan
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'db_peminjaman_atk';
```

### 5. Akses Aplikasi
```
http://localhost/aplikasi_peminjaman/
```

## 📁 Struktur Project

```
aplikasi_peminjaman/
├── assets/
│   ├── css/
│   │   └── style.css          # CSS modern dengan variables
│   ├── js/
│   │   └── script.js          # JavaScript ES6+ class-based
│   └── images/
│       └── favicon.svg        # SVG favicon
├── config/
│   └── database.php           # Konfigurasi database
├── includes/
│   ├── header.php             # Header dengan navigation
│   ├── footer.php             # Footer modern
│   └── functions.php          # Fungsi-fungsi PHP
├── peminjaman_barang/
│   ├── index.php              # Dashboard peminjaman
│   ├── data_peminjaman.php    # Tabel data peminjaman
│   ├── tambah.php             # Form tambah peminjaman
│   └── ...
├── pengambilan_atk/
│   ├── index.php              # Dashboard ATK
│   ├── data_pengambilan.php   # Tabel data ATK
│   └── ...
├── kelas/
│   └── daftar_siswa.php       # Manajemen siswa
├── index.php                  # Dashboard utama
├── database_setup.sql         # Script setup database
└── README.md                  # Dokumentasi
```

## 🎨 Fitur Design Modern

### 📱 Responsive Design
- **Mobile-first approach** dengan breakpoint optimal
- **Touch-friendly** buttons dan navigation
- **Flexible grid system** untuk semua screen size

### 🎭 Modern UI Elements
- **CSS Variables** untuk theming konsisten
- **Glassmorphism effects** dengan backdrop-filter
- **Smooth animations** dan micro-interactions
- **Modern color palette** dengan gradients

### ⚡ Enhanced UX
- **Loading states** dengan spinner animations
- **Toast notifications** untuk feedback
- **Real-time form validation**
- **Auto-hide alerts** dengan smooth transitions

## 📊 Database Schema

### Tabel Utama
- `kelas` - Data kelas sekolah
- `siswa` - Data siswa dengan relasi ke kelas
- `barang` - Master data barang yang bisa dipinjam
- `atk` - Master data alat tulis kantor
- `peminjaman_barang` - Transaksi peminjaman
- `pengambilan_atk` - Transaksi pengambilan ATK

### Views
- `v_laporan_peminjaman` - View untuk laporan peminjaman
- `v_laporan_atk` - View untuk laporan ATK

## 🔧 Troubleshooting

### Database Connection Error
```php
// Pastikan MySQL berjalan
// Cek konfigurasi di config/database.php
// Pastikan database db_peminjaman_atk sudah dibuat
```

### Statistik Menampilkan 0
```php
// Pastikan tabel sudah dibuat dengan benar
// Jalankan database_setup.sql untuk data sample
// Cek koneksi database di dashboard
```

### CSS/JS Tidak Load
```php
// Cek path di includes/header.php
// Pastikan file ada di folder assets/
// Clear browser cache
```

## 🚀 Pengembangan Lanjutan

### Fitur yang Bisa Ditambahkan
- [ ] Sistem login dan autentikasi
- [ ] Role-based access control
- [ ] Notifikasi email/SMS
- [ ] Barcode scanning
- [ ] API REST untuk mobile app
- [ ] Dark mode toggle
- [ ] Multi-language support

### Optimisasi
- [ ] Caching dengan Redis
- [ ] Image optimization
- [ ] Database indexing
- [ ] CDN integration

## 📝 Changelog

### v2.0.0 (2024-01-20)
- ✨ Complete UI/UX redesign dengan modern design
- 📱 Full responsive design untuk mobile
- ⚡ Enhanced JavaScript dengan ES6+ features
- 🎨 CSS modern dengan variables dan animations
- 📊 Real-time statistics dan dashboard
- 🔧 Improved database functions
- 🐛 Bug fixes untuk HTML structure

### v1.0.0 (2024-01-01)
- 🎉 Initial release
- ✅ Basic CRUD operations
- 📊 Simple reporting
- 🎨 Bootstrap-based UI

## 👥 Kontributor

- **Developer**: AI Assistant (Augment Agent)
- **Design**: Modern UI/UX 2024 trends
- **Testing**: Responsive design testing

## 📄 Lisensi

Project ini dibuat untuk tujuan edukasi dan dapat digunakan secara bebas.

## 🆘 Support

Jika mengalami masalah:
1. Cek troubleshooting di atas
2. Pastikan semua persyaratan sistem terpenuhi
3. Cek log error di browser console
4. Pastikan database setup sudah benar

---

**Happy Coding! 🚀**
