<?php 
include_once '../includes/header.php';
include_once '../includes/functions.php';

$sql = "SELECT pa.*, k.nama_kelas, s.nama_siswa 
        FROM pengambilan_atk pa
        JOIN kelas k ON pa.id_kelas = k.id_kelas
        JOIN siswa s ON pa.id_siswa = s.id_siswa
        ORDER BY pa.tanggal DESC";
$stmt = $pdo->query($sql);
$pengambilans = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<h2>Data Pengambilan ATK</h2>

<table class="table table-striped">
    <thead>
        <tr>
            <th>No</th>
            <th>Tanggal</th>
            <th>Kelas</th>
            <th>Nama</th>
            <th>Barang 1</th>
            <th>Jumlah</th>
            <th>Barang 2</th>
            <th>Jumlah</th>
        </tr>
    </thead>
    <tbody>
        <?php $no = 1; foreach($pengambilans as $pengambilan): ?>
        <tr>
            <td><?= $no++ ?></td>
            <td><?= date('d-m-Y', strtotime($pengambilan['tanggal'])) ?></td>
            <td><?= $pengambilan['nama_kelas'] ?></td>
            <td><?= $pengambilan['nama_siswa'] ?></td>
            <td><?= $pengambilan['nama_barang1'] ?></td>
            <td><?= $pengambilan['jumlah_barang1'] ?></td>
            <td><?= $pengambilan['nama_barang2'] ?: '-' ?></td>
            <td><?= $pengambilan['jumlah_barang2'] ?: '-' ?></td>
        </tr>
        <?php endforeach; ?>
    </tbody>
</table>

<a href="index.php" class="btn btn-secondary">Kembali</a>

<?php include_once '../includes/footer.php'; ?>