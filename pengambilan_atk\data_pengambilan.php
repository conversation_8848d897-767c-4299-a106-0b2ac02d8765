<?php
$page_title = "Data Pengambilan ATK";
include_once '../includes/header.php';
include_once '../includes/functions.php';

try {
    $sql = "SELECT pa.*, k.nama_kelas, s.nama_siswa
            FROM pengambilan_atk pa
            LEFT JOIN kelas k ON pa.id_kelas = k.id_kelas
            LEFT JOIN siswa s ON pa.id_siswa = s.id_siswa
            ORDER BY pa.tanggal DESC";
    $stmt = $pdo->query($sql);
    $pengambilans = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch(PDOException $e) {
    $pengambilans = [];
    $error_message = "Error: " . $e->getMessage();
}

// Ambil statistik
$stats = getStatistikATK($pdo);
?>

<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h2 mb-1">
            <i class="bi bi-table text-success me-2"></i>
            Data Pengambilan ATK
        </h1>
        <p class="text-muted mb-0">Kelola dan pantau semua data pengambilan alat tulis kantor</p>
    </div>
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb mb-0">
            <li class="breadcrumb-item"><a href="../index.php">Dashboard</a></li>
            <li class="breadcrumb-item"><a href="index.php">Pengambilan ATK</a></li>
            <li class="breadcrumb-item active">Data Pengambilan</li>
        </ol>
    </nav>
</div>

<!-- Quick Stats -->
<div class="row g-3 mb-4">
    <div class="col-md-3">
        <div class="card bg-success text-white border-0">
            <div class="card-body text-center">
                <h3 class="mb-1"><?= $stats['total'] ?></h3>
                <small>Total Pengambilan</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white border-0">
            <div class="card-body text-center">
                <h3 class="mb-1"><?= $stats['bulan_ini'] ?></h3>
                <small>Bulan Ini</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-primary text-white border-0">
            <div class="card-body text-center">
                <h3 class="mb-1"><?= $stats['jenis_atk'] ?></h3>
                <small>Jenis ATK</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white border-0">
            <div class="card-body text-center">
                <h3 class="mb-1"><?= $stats['stok_menipis'] ?></h3>
                <small>Stok Menipis</small>
            </div>
        </div>
    </div>
</div>

<!-- Error Message -->
<?php if (isset($error_message)): ?>
<div class="alert alert-danger" role="alert">
    <i class="bi bi-exclamation-triangle me-2"></i>
    <?= $error_message ?>
</div>
<?php endif; ?>

<!-- Action Buttons -->
<div class="d-flex justify-content-between align-items-center mb-3">
    <div>
        <a href="tambah.php" class="btn btn-success">
            <i class="bi bi-plus me-2"></i>
            Tambah Pengambilan
        </a>
        <a href="laporan.php" class="btn btn-outline-primary">
            <i class="bi bi-graph-up me-2"></i>
            Lihat Laporan
        </a>
    </div>
    <div>
        <!-- Search Box -->
        <div class="input-group" style="width: 300px;">
            <span class="input-group-text">
                <i class="bi bi-search"></i>
            </span>
            <input type="text" class="form-control" placeholder="Cari data pengambilan..."
                   data-search="#table-pengambilan">
        </div>
    </div>
</div>

<!-- Data Table -->
<div class="card border-0">
    <div class="card-body p-0">
        <?php if (empty($pengambilans)): ?>
        <div class="text-center py-5">
            <i class="bi bi-inbox display-1 text-muted"></i>
            <h4 class="mt-3 text-muted">Belum Ada Data Pengambilan ATK</h4>
            <p class="text-muted">Mulai dengan menambahkan pengambilan ATK baru</p>
            <a href="tambah.php" class="btn btn-success">
                <i class="bi bi-plus me-2"></i>
                Tambah Pengambilan Pertama
            </a>
        </div>
        <?php else: ?>
        <div class="table-responsive">
            <table class="table table-hover mb-0" id="table-pengambilan">
                <thead class="table-light">
                    <tr>
                        <th class="border-0">No</th>
                        <th class="border-0">Tanggal</th>
                        <th class="border-0">Kelas</th>
                        <th class="border-0">Nama Siswa</th>
                        <th class="border-0">Barang 1</th>
                        <th class="border-0">Jumlah</th>
                        <th class="border-0">Barang 2</th>
                        <th class="border-0">Jumlah</th>
                    </tr>
                </thead>
                <tbody>
                    <?php $no = 1; foreach($pengambilans as $pengambilan): ?>
                    <tr>
                        <td><?= $no++ ?></td>
                        <td>
                            <strong><?= formatTanggalIndonesia($pengambilan['tanggal']) ?></strong>
                        </td>
                        <td>
                            <span class="badge bg-light text-dark">
                                <?= $pengambilan['nama_kelas'] ?? 'N/A' ?>
                            </span>
                        </td>
                        <td>
                            <strong><?= $pengambilan['nama_siswa'] ?? 'N/A' ?></strong>
                        </td>
                        <td>
                            <i class="bi bi-pencil me-1"></i>
                            <?= $pengambilan['nama_barang1'] ?? '-' ?>
                        </td>
                        <td>
                            <span class="badge bg-success">
                                <?= $pengambilan['jumlah_barang1'] ?? 0 ?>
                            </span>
                        </td>
                        <td>
                            <?php if($pengambilan['nama_barang2']): ?>
                                <i class="bi bi-pencil me-1"></i>
                                <?= $pengambilan['nama_barang2'] ?>
                            <?php else: ?>
                                <span class="text-muted">-</span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <?php if($pengambilan['jumlah_barang2']): ?>
                                <span class="badge bg-success">
                                    <?= $pengambilan['jumlah_barang2'] ?>
                                </span>
                            <?php else: ?>
                                <span class="text-muted">-</span>
                            <?php endif; ?>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        <?php endif; ?>
    </div>
</div>

<!-- Navigation -->
<div class="d-flex justify-content-between align-items-center mt-4">
    <a href="index.php" class="btn btn-outline-secondary">
        <i class="bi bi-arrow-left me-2"></i>
        Kembali ke Menu ATK
    </a>

    <div class="btn-group">
        <a href="download_laporan.php" class="btn btn-outline-primary">
            <i class="bi bi-download me-2"></i>
            Download CSV
        </a>
        <a href="laporan.php" class="btn btn-outline-success">
            <i class="bi bi-graph-up me-2"></i>
            Lihat Laporan
        </a>
    </div>
</div>

<?php include_once '../includes/footer.php'; ?>