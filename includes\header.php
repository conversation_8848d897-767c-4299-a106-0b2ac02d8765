<?php
// Determine the base path based on current directory
$current_dir = basename(dirname($_SERVER['PHP_SELF']));
$base_path = ($current_dir == 'aplikasi_peminjaman') ? '' : '../';
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Aplikasi Peminjaman & ATK</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link rel="stylesheet" href="<?= $base_path ?>assets/css/style.css">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="<?= $base_path ?>index.php">Sistem Peminjaman</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <div class="navbar-nav ms-auto">
                    <a class="nav-link" href="<?= $base_path ?>peminjaman_barang/index.php">Peminjaman Barang</a>
                    <a class="nav-link" href="<?= $base_path ?>pengambilan_atk/index.php">Pengambilan ATK</a>
                    <a class="nav-link" href="<?= $base_path ?>kelas/daftar_siswa.php">Manajemen Siswa</a>
                </div>
            </div>
        </div>
    </nav>

    <div class="container mt-4">