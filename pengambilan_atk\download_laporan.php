<?php
include_once '../config/database.php';
include_once '../includes/functions.php';

// Set headers untuk download Excel
header('Content-Type: application/vnd.ms-excel');
header('Content-Disposition: attachment; filename="laporan_pengambilan_atk_' . date('Y-m-d') . '.xls"');

// Query data pengambilan ATK
$sql = "SELECT pa.*, k.nama_kelas, s.nama_siswa 
        FROM pengambilan_atk pa
        JOIN kelas k ON pa.id_kelas = k.id_kelas
        JOIN siswa s ON pa.id_siswa = s.id_siswa
        ORDER BY pa.tanggal DESC";
$stmt = $pdo->query($sql);
$pengambilans = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Laporan Pengambilan ATK</title>
</head>
<body>
    <table border="1">
        <tr>
            <th colspan="8" style="background-color: #2196F3; color: white; font-size: 16px; font-weight: bold;">
                LAPORAN PENGAMBILAN ATK
            </th>
        </tr>
        <tr>
            <th colspan="8" style="text-align: left;">
                Tanggal Export: <?= date('d-m-Y H:i:s') ?>
            </th>
        </tr>
        <tr>
            <th style="background-color: #f2f2f2;">No</th>
            <th style="background-color: #f2f2f2;">Tanggal</th>
            <th style="background-color: #f2f2f2;">Kelas</th>
            <th style="background-color: #f2f2f2;">Nama Siswa</th>
            <th style="background-color: #f2f2f2;">Barang 1</th>
            <th style="background-color: #f2f2f2;">Jumlah 1</th>
            <th style="background-color: #f2f2f2;">Barang 2</th>
            <th style="background-color: #f2f2f2;">Jumlah 2</th>
        </tr>
        <?php 
        $no = 1;
        foreach($pengambilans as $pengambilan): 
        ?>
        <tr>
            <td><?= $no++ ?></td>
            <td><?= date('d-m-Y', strtotime($pengambilan['tanggal'])) ?></td>
            <td><?= $pengambilan['nama_kelas'] ?></td>
            <td><?= $pengambilan['nama_siswa'] ?></td>
            <td><?= $pengambilan['nama_barang1'] ?></td>
            <td><?= $pengambilan['jumlah_barang1'] ?></td>
            <td><?= $pengambilan['nama_barang2'] ?: '-' ?></td>
            <td><?= $pengambilan['jumlah_barang2'] ?: '0' ?></td>
        </tr>
        <?php endforeach; ?>
        <tr>
            <th colspan="8" style="text-align: left; font-style: italic;">
                Total Data: <?= count($pengambilans) ?> record
            </th>
        </tr>
    </table>
</body>
</html>
<?php exit; ?>