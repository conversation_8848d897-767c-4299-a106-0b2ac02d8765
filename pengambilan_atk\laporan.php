<?php 
include_once '../includes/header.php';
include_once '../includes/functions.php';

// Filter tanggal (opsional)
$tanggal_awal = $_GET['tanggal_awal'] ?? '';
$tanggal_akhir = $_GET['tanggal_akhir'] ?? '';

// Query data pengambilan ATK dengan filter
$sql = "SELECT pa.*, k.nama_kelas, s.nama_siswa 
        FROM pengambilan_atk pa
        JOIN kelas k ON pa.id_kelas = k.id_kelas
        JOIN siswa s ON pa.id_siswa = s.id_siswa";

$params = [];
if ($tanggal_awal && $tanggal_akhir) {
    $sql .= " WHERE pa.tanggal BETWEEN ? AND ?";
    $params[] = $tanggal_awal;
    $params[] = $tanggal_akhir;
}

$sql .= " ORDER BY pa.tanggal DESC";

$stmt = $pdo->prepare($sql);
$stmt->execute($params);
$pengambilans = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Hitung total pengambilan
$total_pengambilan = count($pengambilans);
?>

<h2>Laporan Pengambilan ATK</h2>

<div class="card mb-4">
    <div class="card-header">
        <h5>Filter Laporan</h5>
    </div>
    <div class="card-body">
        <form method="GET" class="row">
            <div class="col-md-4">
                <label class="form-label">Tanggal Awal</label>
                <input type="date" class="form-control" name="tanggal_awal" value="<?= $tanggal_awal ?>">
            </div>
            <div class="col-md-4">
                <label class="form-label">Tanggal Akhir</label>
                <input type="date" class="form-control" name="tanggal_akhir" value="<?= $tanggal_akhir ?>">
            </div>
            <div class="col-md-4">
                <label class="form-label">&nbsp;</label>
                <div>
                    <button type="submit" class="btn btn-primary">Filter</button>
                    <a href="?tanggal_awal=&tanggal_akhir=" class="btn btn-secondary">Reset</a>
                </div>
            </div>
        </form>
    </div>
</div>

<div class="card mb-4">
    <div class="card-header">
        <h5>Ringkasan Data</h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-12">
                <div class="alert alert-info">
                    <h6>Total Pengambilan ATK</h6>
                    <h3><?= $total_pengambilan ?></h3>
                </div>
            </div>
        </div>
        <!-- Di bagian tombol download, update menjadi: -->
<div class="mt-3">
    <div class="btn-group" role="group">
        <a href="download_laporan.php<?= ($tanggal_awal && $tanggal_akhir) ? '?tanggal_awal=' . $tanggal_awal . '&tanggal_akhir=' . $tanggal_akhir : '' ?>" 
           class="btn btn-success">
            <i class="bi bi-filetype-csv"></i> Download CSV
        </a>
        <a href="download_laporan_excel.php<?= ($tanggal_awal && $tanggal_akhir) ? '?tanggal_awal=' . $tanggal_awal . '&tanggal_akhir=' . $tanggal_akhir : '' ?>" 
           class="btn btn-primary">
            <i class="bi bi-file-earmark-excel"></i> Download Excel
        </a>
    </div>
    <a href="index.php" class="btn btn-secondary">Kembali</a>
</div>

<div class="card">
    <div class="card-header">
        <h5>Data Pengambilan ATK</h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>No</th>
                        <th>Tanggal</th>
                        <th>Kelas</th>
                        <th>Nama Siswa</th>
                        <th>Barang 1</th>
                        <th>Jumlah</th>
                        <th>Barang 2</th>
                        <th>Jumlah</th>
                    </tr>
                </thead>
                <tbody>
                    <?php $no = 1; foreach($pengambilans as $pengambilan): ?>
                    <tr>
                        <td><?= $no++ ?></td>
                        <td><?= date('d-m-Y', strtotime($pengambilan['tanggal'])) ?></td>
                        <td><?= $pengambilan['nama_kelas'] ?></td>
                        <td><?= $pengambilan['nama_siswa'] ?></td>
                        <td><?= $pengambilan['nama_barang1'] ?></td>
                        <td><?= $pengambilan['jumlah_barang1'] ?></td>
                        <td><?= $pengambilan['nama_barang2'] ?: '-' ?></td>
                        <td><?= $pengambilan['jumlah_barang2'] ?: '0' ?></td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<?php include_once '../includes/footer.php'; ?>