<?php
$page_title = "Dashboard";
include_once 'includes/header.php';
include_once 'includes/functions.php';

// Ambil statistik dari database
$stats_peminjaman = getStatistikPeminjaman($pdo);
$stats_atk = getStatistikATK($pdo);
$koneksi_ok = cekKoneksiDatabase($pdo);
?>

<!-- Modern Hero Section -->
<div class="jumbotron text-white py-5 mb-5">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <h1 class="display-4 fw-bold mb-3">
                    <i class="bi bi-box-seam me-3"></i>
                    Sistem Peminjaman Barang & ATK
                </h1>
                <p class="lead mb-4">Aplikasi manajemen modern untuk peminjaman barang dan pengambilan alat tulis kantor dengan interface yang responsif dan user-friendly</p>
                <div class="d-flex flex-wrap gap-2">
                    <span class="badge bg-light text-dark px-3 py-2">
                        <i class="bi bi-check-circle me-1"></i>
                        Responsive Design
                    </span>
                    <span class="badge bg-light text-dark px-3 py-2">
                        <i class="bi bi-shield-check me-1"></i>
                        Data Aman
                    </span>
                    <span class="badge bg-light text-dark px-3 py-2">
                        <i class="bi bi-graph-up me-1"></i>
                        Laporan Real-time
                    </span>
                </div>
            </div>
            <div class="col-lg-4 text-center d-none d-lg-block">
                <i class="bi bi-laptop display-1 opacity-75"></i>
            </div>
        </div>
    </div>
</div>

<!-- Database Connection Status -->
<?php if (!$koneksi_ok): ?>
<div class="alert alert-danger" role="alert">
    <i class="bi bi-exclamation-triangle me-2"></i>
    <strong>Peringatan!</strong> Koneksi database gagal. Pastikan MySQL sudah berjalan dan database sudah dibuat.
</div>
<?php endif; ?>

<!-- Quick Stats -->
<div class="row mb-5">
    <div class="col-md-4 mb-3">
        <div class="card border-0 bg-primary text-white">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <h6 class="text-white-50 mb-1">Peminjaman Aktif</h6>
                        <h2 class="mb-0"><?= $stats_peminjaman['sedang_dipinjam'] ?></h2>
                        <small class="text-white-50">dari <?= $stats_peminjaman['total'] ?> total</small>
                    </div>
                    <div class="ms-3">
                        <i class="bi bi-box" style="font-size: 2.5rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-4 mb-3">
        <div class="card border-0 bg-success text-white">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <h6 class="text-white-50 mb-1">ATK Bulan Ini</h6>
                        <h2 class="mb-0"><?= $stats_atk['bulan_ini'] ?></h2>
                        <small class="text-white-50"><?= $stats_atk['jenis_atk'] ?> jenis tersedia</small>
                    </div>
                    <div class="ms-3">
                        <i class="bi bi-pencil-square" style="font-size: 2.5rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-4 mb-3">
        <div class="card border-0 bg-info text-white">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <h6 class="text-white-50 mb-1">Status Sistem</h6>
                        <h2 class="mb-0"><?= $koneksi_ok ? 'Online' : 'Offline' ?></h2>
                        <small class="text-white-50">Database <?= $koneksi_ok ? 'terhubung' : 'terputus' ?></small>
                    </div>
                    <div class="ms-3">
                        <i class="bi bi-<?= $koneksi_ok ? 'wifi' : 'wifi-off' ?>" style="font-size: 2.5rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Main Menu Cards -->
<h2 class="text-center mb-4 fw-bold">
    <i class="bi bi-grid-3x3-gap me-2"></i>
    Menu Utama
</h2>

<div class="row justify-content-center g-4 mb-5">
    <div class="col-lg-4 col-md-6">
        <div class="card h-100 border-0">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">
                    <i class="bi bi-box me-2"></i>
                    Peminjaman Barang
                </h5>
            </div>
            <div class="card-body text-center d-flex flex-column">
                <div class="mb-3">
                    <i class="bi bi-laptop text-primary" style="font-size: 3rem;"></i>
                </div>
                <p class="card-text flex-grow-1">Kelola peminjaman barang seperti sound system, laptop, proyektor, dan peralatan elektronik lainnya dengan sistem tracking yang akurat.</p>
                <div class="mt-auto">
                    <a href="peminjaman_barang/index.php" class="btn btn-primary btn-lg w-100">
                        <i class="bi bi-arrow-right me-2"></i>
                        Kelola Peminjaman
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-4 col-md-6">
        <div class="card h-100 border-0">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0">
                    <i class="bi bi-pencil me-2"></i>
                    Pengambilan ATK
                </h5>
            </div>
            <div class="card-body text-center d-flex flex-column">
                <div class="mb-3">
                    <i class="bi bi-pencil-square text-success" style="font-size: 3rem;"></i>
                </div>
                <p class="card-text flex-grow-1">Kelola pengambilan alat tulis kantor seperti spidol, tinta, penghapus, kertas, dan perlengkapan kantor lainnya.</p>
                <div class="mt-auto">
                    <a href="pengambilan_atk/index.php" class="btn btn-success btn-lg w-100">
                        <i class="bi bi-arrow-right me-2"></i>
                        Kelola ATK
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-4 col-md-6">
        <div class="card h-100 border-0">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">
                    <i class="bi bi-people me-2"></i>
                    Manajemen Siswa
                </h5>
            </div>
            <div class="card-body text-center d-flex flex-column">
                <div class="mb-3">
                    <i class="bi bi-person-badge text-info" style="font-size: 3rem;"></i>
                </div>
                <p class="card-text flex-grow-1">Kelola data siswa per kelas dengan fitur tambah manual, import CSV, export data, dan manajemen lengkap.</p>
                <div class="mt-auto">
                    <a href="kelas/daftar_siswa.php" class="btn btn-info btn-lg w-100">
                        <i class="bi bi-arrow-right me-2"></i>
                        Kelola Siswa
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- System Information Section -->
<div class="card border-0 mb-4">
    <div class="card-header bg-gradient text-white">
        <h5 class="mb-0">
            <i class="bi bi-info-circle me-2"></i>
            Informasi Sistem
        </h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-8">
                <p class="mb-3">Sistem ini digunakan untuk mengelola peminjaman barang dan pengambilan ATK di sekolah dengan fitur-fitur modern:</p>
                <ul class="list-unstyled">
                    <li class="mb-2">
                        <i class="bi bi-check-circle text-success me-2"></i>
                        Import/Export data siswa menggunakan file CSV
                    </li>
                    <li class="mb-2">
                        <i class="bi bi-check-circle text-success me-2"></i>
                        Tracking real-time status peminjaman
                    </li>
                    <li class="mb-2">
                        <i class="bi bi-check-circle text-success me-2"></i>
                        Laporan dan analitik lengkap
                    </li>
                    <li class="mb-2">
                        <i class="bi bi-check-circle text-success me-2"></i>
                        Interface responsive untuk semua device
                    </li>
                </ul>
            </div>
            <div class="col-md-4 text-center">
                <div class="alert alert-warning border-0">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    <strong>Penting!</strong><br>
                    Pastikan data siswa sudah lengkap sebelum menggunakan fitur peminjaman dan pengambilan.
                </div>
            </div>
        </div>
    </div>
</div>

<?php include_once 'includes/footer.php'; ?>