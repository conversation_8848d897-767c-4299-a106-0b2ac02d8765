<?php
$page_title = "Data Peminjaman Barang";
include_once '../includes/header.php';
include_once '../includes/functions.php';

try {
    $sql = "SELECT pb.*, k.nama_kelas, s.nama_siswa, b.nama_barang
            FROM peminjaman_barang pb
            LEFT JOIN kelas k ON pb.id_kelas = k.id_kelas
            LEFT JOIN siswa s ON pb.id_siswa = s.id_siswa
            LEFT JOIN barang b ON pb.id_barang = b.id_barang
            ORDER BY pb.tanggal_pinjam DESC";
    $stmt = $pdo->query($sql);
    $peminjamans = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch(PDOException $e) {
    $peminjamans = [];
    $error_message = "Error: " . $e->getMessage();
}

// Ambil statistik
$stats = getStatistikPeminjaman($pdo);
?>

<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h2 mb-1">
            <i class="bi bi-table text-primary me-2"></i>
            Data Peminjaman Barang
        </h1>
        <p class="text-muted mb-0">Kelola dan pantau semua data peminjaman barang</p>
    </div>
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb mb-0">
            <li class="breadcrumb-item"><a href="../index.php">Dashboard</a></li>
            <li class="breadcrumb-item"><a href="index.php">Peminjaman Barang</a></li>
            <li class="breadcrumb-item active">Data Peminjaman</li>
        </ol>
    </nav>
</div>

<!-- Quick Stats -->
<div class="row g-3 mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white border-0">
            <div class="card-body text-center">
                <h3 class="mb-1"><?= $stats['total'] ?></h3>
                <small>Total Peminjaman</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white border-0">
            <div class="card-body text-center">
                <h3 class="mb-1"><?= $stats['sedang_dipinjam'] ?></h3>
                <small>Sedang Dipinjam</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white border-0">
            <div class="card-body text-center">
                <h3 class="mb-1"><?= $stats['dikembalikan'] ?></h3>
                <small>Sudah Dikembalikan</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-danger text-white border-0">
            <div class="card-body text-center">
                <h3 class="mb-1"><?= $stats['terlambat'] ?></h3>
                <small>Terlambat</small>
            </div>
        </div>
    </div>
</div>

<!-- Error Message -->
<?php if (isset($error_message)): ?>
<div class="alert alert-danger" role="alert">
    <i class="bi bi-exclamation-triangle me-2"></i>
    <?= $error_message ?>
</div>
<?php endif; ?>

<!-- Action Buttons -->
<div class="d-flex justify-content-between align-items-center mb-3">
    <div>
        <a href="tambah.php" class="btn btn-primary">
            <i class="bi bi-plus me-2"></i>
            Tambah Peminjaman
        </a>
        <a href="laporan.php" class="btn btn-outline-success">
            <i class="bi bi-graph-up me-2"></i>
            Lihat Laporan
        </a>
    </div>
    <div>
        <!-- Search Box -->
        <div class="input-group" style="width: 300px;">
            <span class="input-group-text">
                <i class="bi bi-search"></i>
            </span>
            <input type="text" class="form-control" placeholder="Cari data peminjaman..."
                   data-search="#table-peminjaman">
        </div>
    </div>
</div>

<!-- Data Table -->
<div class="card border-0">
    <div class="card-body p-0">
        <?php if (empty($peminjamans)): ?>
        <div class="text-center py-5">
            <i class="bi bi-inbox display-1 text-muted"></i>
            <h4 class="mt-3 text-muted">Belum Ada Data Peminjaman</h4>
            <p class="text-muted">Mulai dengan menambahkan peminjaman barang baru</p>
            <a href="tambah.php" class="btn btn-primary">
                <i class="bi bi-plus me-2"></i>
                Tambah Peminjaman Pertama
            </a>
        </div>
        <?php else: ?>
        <div class="table-responsive">
            <table class="table table-hover mb-0" id="table-peminjaman">
                <thead class="table-light">
                    <tr>
                        <th class="border-0">No</th>
                        <th class="border-0">Tanggal Pinjam</th>
                        <th class="border-0">Tanggal Kembali</th>
                        <th class="border-0">Kelas</th>
                        <th class="border-0">Nama Siswa</th>
                        <th class="border-0">Barang</th>
                        <th class="border-0">Status</th>
                        <th class="border-0">Aksi</th>
                    </tr>
                </thead>
                <tbody>
                    <?php $no = 1; foreach($peminjamans as $peminjaman): ?>
                    <tr>
                        <td><?= $no++ ?></td>
                        <td>
                            <strong><?= formatTanggalIndonesia($peminjaman['tanggal_pinjam']) ?></strong>
                        </td>
                        <td>
                            <?php if($peminjaman['tanggal_kembali']): ?>
                                <?= formatTanggalIndonesia($peminjaman['tanggal_kembali']) ?>
                            <?php else: ?>
                                <span class="text-muted">
                                    <i class="bi bi-dash-circle me-1"></i>
                                    Belum dikembalikan
                                </span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <span class="badge bg-light text-dark">
                                <?= $peminjaman['nama_kelas'] ?? 'N/A' ?>
                            </span>
                        </td>
                        <td>
                            <strong><?= $peminjaman['nama_siswa'] ?? 'N/A' ?></strong>
                        </td>
                        <td>
                            <i class="bi bi-box me-1"></i>
                            <?= $peminjaman['nama_barang'] ?? 'N/A' ?>
                        </td>
                        <td>
                            <?php if($peminjaman['status'] == 'dipinjam'): ?>
                                <span class="badge bg-warning text-dark">
                                    <i class="bi bi-clock me-1"></i>
                                    Dipinjam
                                </span>
                                <?php
                                // Cek keterlambatan
                                if($peminjaman['tanggal_kembali'] && strtotime($peminjaman['tanggal_kembali']) < strtotime('today')): ?>
                                    <br><span class="badge bg-danger mt-1">
                                        <i class="bi bi-exclamation-triangle me-1"></i>
                                        Terlambat
                                    </span>
                                <?php endif; ?>
                            <?php else: ?>
                                <span class="badge bg-success">
                                    <i class="bi bi-check-circle me-1"></i>
                                    Dikembalikan
                                </span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <?php if($peminjaman['status'] == 'dipinjam'): ?>
                                <a href="kembalikan.php?id=<?= $peminjaman['id_pinjam'] ?>"
                                   class="btn btn-sm btn-success"
                                   data-bs-toggle="tooltip"
                                   title="Kembalikan barang">
                                    <i class="bi bi-check-circle me-1"></i>
                                    Kembalikan
                                </a>
                            <?php else: ?>
                                <span class="text-muted">
                                    <i class="bi bi-check-all"></i>
                                    Selesai
                                </span>
                            <?php endif; ?>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        <?php endif; ?>
    </div>
</div>

<!-- Navigation -->
<div class="d-flex justify-content-between align-items-center mt-4">
    <a href="index.php" class="btn btn-outline-secondary">
        <i class="bi bi-arrow-left me-2"></i>
        Kembali ke Menu Peminjaman
    </a>

    <div class="btn-group">
        <a href="download_laporan.php" class="btn btn-outline-primary">
            <i class="bi bi-download me-2"></i>
            Download CSV
        </a>
        <a href="laporan.php" class="btn btn-outline-success">
            <i class="bi bi-graph-up me-2"></i>
            Lihat Laporan
        </a>
    </div>
</div>

<?php include_once '../includes/footer.php'; ?>