<?php 
include_once '../includes/header.php';
include_once '../includes/functions.php';

$sql = "SELECT pb.*, k.nama_kelas, s.nama_siswa, b.nama_barang 
        FROM peminjaman_barang pb
        JOIN kelas k ON pb.id_kelas = k.id_kelas
        JOIN siswa s ON pb.id_siswa = s.id_siswa
        JOIN barang b ON pb.id_barang = b.id_barang
        ORDER BY pb.tanggal_pinjam DESC";
$stmt = $pdo->query($sql);
$peminjamans = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<h2>Data Peminjaman Barang</h2>

<table class="table table-striped">
    <thead>
        <tr>
            <th>No</th>
            <th>Tanggal Pinjam</th>
            <th>Tanggal Kembali Rencana</th>
            <th>Kelas</th>
            <th>Nama</th>
            <th>Barang</th>
            <th>Status</th>
            <th>Aksi</th>
        </tr>
    </thead>
    <tbody>
        <?php $no = 1; foreach($peminjamans as $peminjaman): ?>
        <tr>
            <td><?= $no++ ?></td>
            <td><?= date('d-m-Y', strtotime($peminjaman['tanggal_pinjam'])) ?></td>
            <td>
                <?php if($peminjaman['tanggal_kembali']): ?>
                    <?= date('d-m-Y', strtotime($peminjaman['tanggal_kembali'])) ?>
                <?php else: ?>
                    <span class="text-muted">Belum diatur</span>
                <?php endif; ?>
            </td>
            <td><?= $peminjaman['nama_kelas'] ?></td>
            <td><?= $peminjaman['nama_siswa'] ?></td>
            <td><?= $peminjaman['nama_barang'] ?></td>
            <td>
                <?php if($peminjaman['status'] == 'dipinjam'): ?>
                    <span class="badge bg-warning">Dipinjam</span>
                    <?php 
                    // Cek keterlambatan
                    if($peminjaman['tanggal_kembali'] && strtotime($peminjaman['tanggal_kembali']) < strtotime('today')): ?>
                        <span class="badge bg-danger">Terlambat</span>
                    <?php endif; ?>
                <?php else: ?>
                    <span class="badge bg-success">Dikembalikan</span>
                <?php endif; ?>
            </td>
            <td>
                <?php if($peminjaman['status'] == 'dipinjam'): ?>
                    <a href="kembalikan.php?id=<?= $peminjaman['id_pinjam'] ?>" class="btn btn-sm btn-success">Kembalikan</a>
                <?php endif; ?>
            </td>
        </tr>
        <?php endforeach; ?>
    </tbody>
</table>

<a href="index.php" class="btn btn-secondary">Kembali</a>

<?php include_once '../includes/footer.php'; ?>