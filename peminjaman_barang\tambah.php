<?php 
include_once '../includes/header.php';
include_once '../includes/functions.php';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $tanggal_pinjam = $_POST['tanggal_pinjam'];
    $id_kelas = $_POST['id_kelas'];
    $id_siswa = $_POST['id_siswa'];
    $id_barang = $_POST['id_barang'];
    $tanggal_kembali_rencana = $_POST['tanggal_kembali_rencana']; // Tanggal kembali yang direncanakan
    
    $sql = "INSERT INTO peminjaman_barang (tanggal_pinjam, id_kelas, id_siswa, id_barang, tanggal_kembali, status) 
            VALUES (?, ?, ?, ?, ?, 'dipinjam')";
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$tanggal_pinjam, $id_kelas, $id_siswa, $id_barang, $tanggal_kembali_rencana]);
    
    echo "<div class='alert alert-success'>Peminjaman berhasil ditambahkan!</div>";
}
?>

<h2>Tambah Peminjaman Barang</h2>

<form method="POST">
    <div class="mb-3">
        <label class="form-label">Tanggal Pinjam</label>
        <input type="date" class="form-control" name="tanggal_pinjam" value="<?= date('Y-m-d') ?>" required>
    </div>
    
    <div class="mb-3">
        <label class="form-label">Tanggal Kembali (Rencana)</label>
        <input type="date" class="form-control" name="tanggal_kembali_rencana" 
               min="<?= date('Y-m-d', strtotime('+1 day')) ?>" 
               value="<?= date('Y-m-d', strtotime('+7 days')) ?>">
        <div class="form-text">Tanggal rencana pengembalian barang</div>
    </div>
    
    <div class="mb-3">
        <label class="form-label">Kelas</label>
        <select class="form-select" name="id_kelas" id="kelas" required>
            <option value="">Pilih Kelas</option>
            <?php foreach(getKelas($pdo) as $kelas): ?>
                <option value="<?= $kelas['id_kelas'] ?>"><?= $kelas['nama_kelas'] ?></option>
            <?php endforeach; ?>
        </select>
    </div>
    
    <div class="mb-3">
        <label class="form-label">Nama Siswa</label>
        <select class="form-select" name="id_siswa" id="nama_siswa" required>
            <option value="">Pilih Nama Siswa</option>
        </select>
    </div>
    
    <div class="mb-3">
        <label class="form-label">Barang</label>
        <select class="form-select" name="id_barang" required>
            <option value="">Pilih Barang</option>
            <?php foreach(getBarang($pdo) as $barang): ?>
                <option value="<?= $barang['id_barang'] ?>"><?= $barang['nama_barang'] ?></option>
            <?php endforeach; ?>
        </select>
    </div>
    
    <button type="submit" class="btn btn-primary">Simpan</button>
    <a href="index.php" class="btn btn-secondary">Kembali</a>
</form>

<script>
document.getElementById('kelas').addEventListener('change', function() {
    var id_kelas = this.value;
    var namaSiswaSelect = document.getElementById('nama_siswa');
    
    if(id_kelas) {
        fetch('../kelas/get_nama.php?id_kelas=' + id_kelas)
            .then(response => response.json())
            .then(data => {
                namaSiswaSelect.innerHTML = '<option value="">Pilih Nama Siswa</option>';
                data.forEach(function(siswa) {
                    namaSiswaSelect.innerHTML += '<option value="' + siswa.id_siswa + '">' + siswa.nama_siswa + '</option>';
                });
            });
    } else {
        namaSiswaSelect.innerHTML = '<option value="">Pilih Nama Siswa</option>';
    }
});
</script>

<?php include_once '../includes/footer.php'; ?>