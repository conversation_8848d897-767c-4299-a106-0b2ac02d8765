<?php 
include_once '../config/database.php';

// Ambil ID peminjaman dari parameter URL
$id_pinjam = $_GET['id'] ?? 0;

if (!$id_pinjam) {
    header('Location: data_peminjaman.php');
    exit;
}

try {
    // Update status peminjaman menjadi "dikembalikan"
    $sql = "UPDATE peminjaman_barang SET tanggal_kembali = ?, status = 'dikembalikan' WHERE id_pinjam = ?";
    $stmt = $pdo->prepare($sql);
    $stmt->execute([date('Y-m-d'), $id_pinjam]);

    // Redirect ke halaman data peminjaman setelah berhasil
    header('Location: data_peminjaman.php');
    exit;
} catch(PDOException $e) {
    echo "Error: " . $e->getMessage();
}
?>