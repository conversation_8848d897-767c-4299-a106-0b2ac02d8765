<?php 
include_once '../includes/header.php';
include_once '../includes/functions.php';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $nama_siswa = $_POST['nama_siswa'];
    $id_kelas = $_POST['id_kelas'];
    
    try {
        $sql = "INSERT INTO siswa (nama_siswa, id_kelas) VALUES (?, ?)";
        $stmt = $pdo->prepare($sql);
        $stmt->execute([$nama_siswa, $id_kelas]);
        
        $success_message = "Siswa berhasil ditambahkan!";
    } catch(PDOException $e) {
        $error_message = "Error: " . $e->getMessage();
    }
}
?>

<h2>Tambah Siswa</h2>

<?php if(isset($success_message)): ?>
    <div class="alert alert-success"><?= $success_message ?></div>
<?php endif; ?>

<?php if(isset($error_message)): ?>
    <div class="alert alert-danger"><?= $error_message ?></div>
<?php endif; ?>

<form method="POST">
    <div class="mb-3">
        <label class="form-label">Nama Siswa</label>
        <input type="text" class="form-control" name="nama_siswa" required>
    </div>
    
    <div class="mb-3">
        <label class="form-label">Kelas</label>
        <select class="form-select" name="id_kelas" required>
            <option value="">Pilih Kelas</option>
            <?php foreach(getKelas($pdo) as $kelas): ?>
                <option value="<?= $kelas['id_kelas'] ?>"><?= $kelas['nama_kelas'] ?></option>
            <?php endforeach; ?>
        </select>
    </div>
    
    <button type="submit" class="btn btn-primary">Tambah Siswa</button>
    <a href="daftar_siswa.php" class="btn btn-secondary">Lihat Daftar Siswa</a>
    <a href="../index.php" class="btn btn-outline-secondary">Kembali</a>
</form>

<?php include_once '../includes/footer.php'; ?>