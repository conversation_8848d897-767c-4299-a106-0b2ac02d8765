<?php 
include_once '../includes/header.php';
include_once '../includes/functions.php';

// Filter tanggal (opsional)
$tanggal_awal = $_GET['tanggal_awal'] ?? '';
$tanggal_akhir = $_GET['tanggal_akhir'] ?? '';

// Query data peminjaman barang dengan filter
$sql = "SELECT pb.*, k.nama_kelas, s.nama_siswa, b.nama_barang 
        FROM peminjaman_barang pb
        JOIN kelas k ON pb.id_kelas = k.id_kelas
        JOIN siswa s ON pb.id_siswa = s.id_siswa
        JOIN barang b ON pb.id_barang = b.id_barang";

$params = [];
if ($tanggal_awal && $tanggal_akhir) {
    $sql .= " WHERE pb.tanggal_pinjam BETWEEN ? AND ?";
    $params[] = $tanggal_awal;
    $params[] = $tanggal_akhir;
}

$sql .= " ORDER BY pb.tanggal_pinjam DESC";

$stmt = $pdo->prepare($sql);
$stmt->execute($params);
$peminjamans = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Hitung total peminjaman
$total_peminjaman = count($peminjamans);

// Hitung jumlah barang yang masih dipinjam
$barang_dipinjam = 0;
$barang_dikembalikan = 0;
foreach($peminjamans as $peminjaman) {
    if($peminjaman['status'] == 'dipinjam') {
        $barang_dipinjam++;
    } else {
        $barang_dikembalikan++;
    }
}
?>

<h2>Laporan Peminjaman Barang</h2>

<div class="card mb-4">
    <div class="card-header">
        <h5>Filter Laporan</h5>
    </div>
    <div class="card-body">
        <form method="GET" class="row">
            <div class="col-md-4">
                <label class="form-label">Tanggal Awal</label>
                <input type="date" class="form-control" name="tanggal_awal" value="<?= $tanggal_awal ?>">
            </div>
            <div class="col-md-4">
                <label class="form-label">Tanggal Akhir</label>
                <input type="date" class="form-control" name="tanggal_akhir" value="<?= $tanggal_akhir ?>">
            </div>
            <div class="col-md-4">
                <label class="form-label">&nbsp;</label>
                <div>
                    <button type="submit" class="btn btn-primary">Filter</button>
                    <a href="?tanggal_awal=&tanggal_akhir=" class="btn btn-secondary">Reset</a>
                </div>
            </div>
        </form>
    </div>
</div>

<div class="card mb-4">
    <div class="card-header">
        <h5>Ringkasan Data</h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-4">
                <div class="alert alert-info">
                    <h6>Total Peminjaman</h6>
                    <h3><?= $total_peminjaman ?></h3>
                </div>
            </div>
            <div class="col-md-4">
                <div class="alert alert-warning">
                    <h6>Barang Dipinjam</h6>
                    <h3><?= $barang_dipinjam ?></h3>
                </div>
            </div>
            <div class="col-md-4">
                <div class="alert alert-success">
                    <h6>Barang Dikembalikan</h6>
                    <h3><?= $barang_dikembalikan ?></h3>
                </div>
            </div>
        </div>
        
        <!-- Di bagian tombol download, update menjadi: -->
<div class="mt-3">
    <div class="btn-group" role="group">
        <a href="download_laporan.php<?= ($tanggal_awal && $tanggal_akhir) ? '?tanggal_awal=' . $tanggal_awal . '&tanggal_akhir=' . $tanggal_akhir : '' ?>" 
           class="btn btn-success">
            <i class="bi bi-filetype-csv"></i> Download CSV
        </a>
        <a href="download_laporan_excel.php<?= ($tanggal_awal && $tanggal_akhir) ? '?tanggal_awal=' . $tanggal_awal . '&tanggal_akhir=' . $tanggal_akhir : '' ?>" 
           class="btn btn-primary">
            <i class="bi bi-file-earmark-excel"></i> Download Excel
        </a>
    </div>
    <a href="index.php" class="btn btn-secondary">Kembali</a>
</div>

<div class="card">
    <div class="card-header">
        <h5>Data Peminjaman</h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>No</th>
                        <th>Tanggal Pinjam</th>
                        <th>Tanggal Kembali</th>
                        <th>Kelas</th>
                        <th>Nama Siswa</th>
                        <th>Barang</th>
                        <th>Status</th>
                    </tr>
                </thead>
                <tbody>
                    <?php $no = 1; foreach($peminjamans as $peminjaman): ?>
                    <tr>
                        <td><?= $no++ ?></td>
                        <td><?= date('d-m-Y', strtotime($peminjaman['tanggal_pinjam'])) ?></td>
                        <td>
                            <?php if($peminjaman['tanggal_kembali']): ?>
                                <?= date('d-m-Y', strtotime($peminjaman['tanggal_kembali'])) ?>
                            <?php else: ?>
                                <span class="text-muted">-</span>
                            <?php endif; ?>
                        </td>
                        <td><?= $peminjaman['nama_kelas'] ?></td>
                        <td><?= $peminjaman['nama_siswa'] ?></td>
                        <td><?= $peminjaman['nama_barang'] ?></td>
                        <td>
                            <?php if($peminjaman['status'] == 'dipinjam'): ?>
                                <span class="badge bg-warning">Dipinjam</span>
                            <?php else: ?>
                                <span class="badge bg-success">Dikembalikan</span>
                            <?php endif; ?>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<?php include_once '../includes/footer.php'; ?>