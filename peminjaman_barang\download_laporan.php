<?php
include_once '../config/database.php';
include_once '../includes/functions.php';

// Set headers untuk download Excel
header('Content-Type: application/vnd.ms-excel');
header('Content-Disposition: attachment; filename="laporan_peminjaman_barang_' . date('Y-m-d') . '.xls"');

// Query data peminjaman barang
$sql = "SELECT pb.*, k.nama_kelas, s.nama_siswa, b.nama_barang 
        FROM peminjaman_barang pb
        JOIN kelas k ON pb.id_kelas = k.id_kelas
        JOIN siswa s ON pb.id_siswa = s.id_siswa
        JOIN barang b ON pb.id_barang = b.id_barang
        ORDER BY pb.tanggal_pinjam DESC";
$stmt = $pdo->query($sql);
$peminjamans = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title><PERSON><PERSON><PERSON>emin<PERSON>ang</title>
</head>
<body>
    <table border="1">
        <tr>
            <th colspan="8" style="background-color: #4CAF50; color: white; font-size: 16px; font-weight: bold;">
                LAPORAN PEMINJAMAN BARANG
            </th>
        </tr>
        <tr>
            <th colspan="8" style="text-align: left;">
                Tanggal Export: <?= date('d-m-Y H:i:s') ?>
            </th>
        </tr>
        <tr>
            <th style="background-color: #f2f2f2;">No</th>
            <th style="background-color: #f2f2f2;">Tanggal Pinjam</th>
            <th style="background-color: #f2f2f2;">Tanggal Kembali</th>
            <th style="background-color: #f2f2f2;">Kelas</th>
            <th style="background-color: #f2f2f2;">Nama Siswa</th>
            <th style="background-color: #f2f2f2;">Barang</th>
            <th style="background-color: #f2f2f2;">Status</th>
            <th style="background-color: #f2f2f2;">Keterangan</th>
        </tr>
        <?php 
        $no = 1;
        foreach($peminjamans as $peminjaman): 
            $tanggal_kembali = $peminjaman['tanggal_kembali'] ? date('d-m-Y', strtotime($peminjaman['tanggal_kembali'])) : 'Belum Dikembalikan';
            $status = $peminjaman['status'] == 'dipinjam' ? 'Dipinjam' : 'Dikembalikan';
            $keterangan = '';
            if($peminjaman['status'] == 'dipinjam' && $peminjaman['tanggal_kembali']) {
                $tgl_sekarang = strtotime('today');
                $tgl_kembali = strtotime($peminjaman['tanggal_kembali']);
                if($tgl_kembali < $tgl_sekarang) {
                    $keterangan = 'TERLAMBAT';
                }
            }
        ?>
        <tr>
            <td><?= $no++ ?></td>
            <td><?= date('d-m-Y', strtotime($peminjaman['tanggal_pinjam'])) ?></td>
            <td><?= $tanggal_kembali ?></td>
            <td><?= $peminjaman['nama_kelas'] ?></td>
            <td><?= $peminjaman['nama_siswa'] ?></td>
            <td><?= $peminjaman['nama_barang'] ?></td>
            <td><?= $status ?></td>
            <td><?= $keterangan ?></td>
        </tr>
        <?php endforeach; ?>
        <tr>
            <th colspan="8" style="text-align: left; font-style: italic;">
                Total Data: <?= count($peminjamans) ?> record
            </th>
        </tr>
    </table>
</body>
</html>
<?php exit; ?>