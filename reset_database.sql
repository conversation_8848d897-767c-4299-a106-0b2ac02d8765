-- Reset Database untuk Aplikasi Peminjaman Barang & ATK
-- Jalankan script ini jika database sudah ada dan perlu direset

-- Hapus database lama jika ada
DROP DATABASE IF EXISTS db_peminjaman_atk;

-- Buat database baru
CREATE DATABASE db_peminjaman_atk;
USE db_peminjaman_atk;

-- <PERSON>bel kelas
CREATE TABLE kelas (
    id_kelas INT AUTO_INCREMENT PRIMARY KEY,
    nama_kelas VARCHAR(50) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- <PERSON><PERSON> siswa
CREATE TABLE siswa (
    id_siswa INT AUTO_INCREMENT PRIMARY KEY,
    id_kelas INT,
    nama_siswa VARCHAR(100) NOT NULL,
    nis VARCHAR(20),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (id_kelas) REFERENCES kelas(id_kelas) ON DELETE CASCADE
);

-- <PERSON><PERSON> barang
CREATE TABLE barang (
    id_barang INT AUTO_INCREMENT PRIMARY KEY,
    nama_barang VARCHAR(100) NOT NULL,
    kategori VARCHAR(50),
    kondisi ENUM('baik', 'rusak', 'maintenance') DEFAULT 'baik',
    stok INT DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tabel ATK
CREATE TABLE atk (
    id_atk INT AUTO_INCREMENT PRIMARY KEY,
    nama_atk VARCHAR(100) NOT NULL,
    kategori VARCHAR(50),
    satuan VARCHAR(20) DEFAULT 'pcs',
    stok INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tabel peminjaman_barang
CREATE TABLE peminjaman_barang (
    id_pinjam INT AUTO_INCREMENT PRIMARY KEY,
    id_kelas INT,
    id_siswa INT,
    id_barang INT,
    tanggal_pinjam DATE NOT NULL,
    tanggal_kembali DATE,
    tanggal_kembali_aktual DATE NULL,
    status ENUM('dipinjam', 'dikembalikan') DEFAULT 'dipinjam',
    keterangan TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (id_kelas) REFERENCES kelas(id_kelas),
    FOREIGN KEY (id_siswa) REFERENCES siswa(id_siswa),
    FOREIGN KEY (id_barang) REFERENCES barang(id_barang)
);

-- Tabel pengambilan_atk (struktur yang sesuai dengan aplikasi)
CREATE TABLE pengambilan_atk (
    id_ambil INT AUTO_INCREMENT PRIMARY KEY,
    tanggal DATE NOT NULL,
    id_kelas INT,
    id_siswa INT,
    nama_barang1 VARCHAR(100),
    jumlah_barang1 INT DEFAULT 0,
    nama_barang2 VARCHAR(100),
    jumlah_barang2 INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (id_kelas) REFERENCES kelas(id_kelas),
    FOREIGN KEY (id_siswa) REFERENCES siswa(id_siswa)
);

-- Insert data sample
INSERT INTO kelas (nama_kelas) VALUES 
('X IPA 1'), ('X IPA 2'), ('X IPS 1'), ('X IPS 2'),
('XI IPA 1'), ('XI IPA 2'), ('XI IPS 1'), ('XI IPS 2'),
('XII IPA 1'), ('XII IPA 2'), ('XII IPS 1'), ('XII IPS 2');

INSERT INTO siswa (id_kelas, nama_siswa, nis) VALUES 
(1, 'Ahmad Rizki', '2024001'),
(1, 'Siti Nurhaliza', '2024002'),
(1, 'Budi Santoso', '2024003'),
(2, 'Dewi Sartika', '2024004'),
(2, 'Eko Prasetyo', '2024005'),
(3, 'Fitri Handayani', '2024006'),
(3, 'Andi Wijaya', '2024007'),
(4, 'Maya Sari', '2024008');

INSERT INTO barang (nama_barang, kategori, kondisi, stok) VALUES 
('Laptop Asus', 'Elektronik', 'baik', 5),
('Proyektor Epson', 'Elektronik', 'baik', 3),
('Sound System', 'Audio', 'baik', 2),
('Kamera DSLR', 'Elektronik', 'baik', 1),
('Microphone Wireless', 'Audio', 'baik', 4),
('Extension Cable', 'Aksesoris', 'baik', 10);

INSERT INTO atk (nama_atk, kategori, satuan, stok) VALUES 
('Spidol Whiteboard', 'Alat Tulis', 'pcs', 8),    -- Stok menipis
('Penghapus Whiteboard', 'Alat Tulis', 'pcs', 20),
('Kertas A4', 'Kertas', 'rim', 25),
('Tinta Printer HP', 'Tinta', 'cartridge', 5),    -- Stok menipis
('Stapler', 'Alat Kantor', 'pcs', 3),             -- Stok menipis
('Paper Clip', 'Alat Kantor', 'box', 12),
('Correction Pen', 'Alat Tulis', 'pcs', 30);

-- Insert sample peminjaman
INSERT INTO peminjaman_barang (id_kelas, id_siswa, id_barang, tanggal_pinjam, tanggal_kembali, status) VALUES 
(1, 1, 1, '2024-01-15', '2024-01-20', 'dipinjam'),
(1, 2, 2, '2024-01-10', '2024-01-15', 'dikembalikan'),
(2, 4, 3, '2024-01-12', '2024-01-18', 'dipinjam'),
(2, 5, 4, '2024-01-08', '2024-01-12', 'dikembalikan'),
(3, 6, 5, '2024-01-20', '2024-01-25', 'dipinjam');

-- Insert sample pengambilan ATK
INSERT INTO pengambilan_atk (tanggal, id_kelas, id_siswa, nama_barang1, jumlah_barang1, nama_barang2, jumlah_barang2) VALUES 
('2024-01-15', 1, 1, 'Spidol Whiteboard', 2, 'Kertas A4', 1),
('2024-01-16', 1, 2, 'Penghapus Whiteboard', 1, '', 0),
('2024-01-17', 2, 4, 'Tinta Printer HP', 1, 'Stapler', 1),
('2024-01-18', 2, 5, 'Paper Clip', 2, 'Correction Pen', 3),
('2024-01-19', 3, 6, 'Spidol Whiteboard', 1, 'Kertas A4', 2),
('2024-01-20', 3, 7, 'Penghapus Whiteboard', 2, 'Tinta Printer HP', 1),
('2024-01-21', 4, 8, 'Stapler', 1, 'Paper Clip', 1);

-- Buat index untuk performa
CREATE INDEX idx_peminjaman_status ON peminjaman_barang(status);
CREATE INDEX idx_peminjaman_tanggal ON peminjaman_barang(tanggal_pinjam);
CREATE INDEX idx_pengambilan_tanggal ON pengambilan_atk(tanggal);
CREATE INDEX idx_siswa_kelas ON siswa(id_kelas);

-- Views untuk laporan
CREATE VIEW v_laporan_peminjaman AS
SELECT 
    pb.id_pinjam,
    k.nama_kelas,
    s.nama_siswa,
    s.nis,
    b.nama_barang,
    b.kategori,
    pb.tanggal_pinjam,
    pb.tanggal_kembali,
    pb.tanggal_kembali_aktual,
    pb.status,
    pb.keterangan,
    CASE 
        WHEN pb.status = 'dipinjam' AND pb.tanggal_kembali < CURDATE() THEN 'Terlambat'
        WHEN pb.status = 'dipinjam' THEN 'Aktif'
        ELSE 'Selesai'
    END as status_detail
FROM peminjaman_barang pb
LEFT JOIN kelas k ON pb.id_kelas = k.id_kelas
LEFT JOIN siswa s ON pb.id_siswa = s.id_siswa
LEFT JOIN barang b ON pb.id_barang = b.id_barang;

CREATE VIEW v_laporan_atk AS
SELECT 
    pa.id_ambil,
    pa.tanggal,
    k.nama_kelas,
    s.nama_siswa,
    s.nis,
    pa.nama_barang1,
    pa.jumlah_barang1,
    pa.nama_barang2,
    pa.jumlah_barang2,
    pa.created_at
FROM pengambilan_atk pa
LEFT JOIN kelas k ON pa.id_kelas = k.id_kelas
LEFT JOIN siswa s ON pa.id_siswa = s.id_siswa;

-- Tampilkan hasil
SELECT 'Database reset completed successfully!' as status;
SELECT COUNT(*) as total_kelas FROM kelas;
SELECT COUNT(*) as total_siswa FROM siswa;
SELECT COUNT(*) as total_barang FROM barang;
SELECT COUNT(*) as total_atk FROM atk;
SELECT COUNT(*) as total_peminjaman FROM peminjaman_barang;
SELECT COUNT(*) as total_pengambilan_atk FROM pengambilan_atk;
SELECT COUNT(*) as atk_stok_menipis FROM atk WHERE stok < 10;
