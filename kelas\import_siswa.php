<?php 
include_once '../includes/header.php';
include_once '../includes/functions.php';

$message = '';
$message_type = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_FILES['file_excel'])) {
    $file = $_FILES['file_excel'];
    $file_type = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    
    // Validasi file
    if ($file['error'] == 0) {
        if ($file_type == 'csv') {
            // Proses file CSV
            if (($handle = fopen($file['tmp_name'], 'r')) !== FALSE) {
                $success_count = 0;
                $error_count = 0;
                $row = 0;
                
                while (($data = fgetcsv($handle, 1000, ',')) !== FALSE) {
                    $row++;
                    // Lewati header jika ada
                    if ($row == 1 && (strtolower($data[0]) == 'nama' || strtolower($data[0]) == 'nama_siswa')) {
                        continue;
                    }
                    
                    if (count($data) >= 2) {
                        $nama_siswa = trim($data[0]);
                        $nama_kelas = trim($data[1]);
                        
                        // Cari id_kelas
                        $stmt = $pdo->prepare("SELECT id_kelas FROM kelas WHERE nama_kelas = ?");
                        $stmt->execute([$nama_kelas]);
                        $kelas = $stmt->fetch(PDO::FETCH_ASSOC);
                        
                        if ($kelas) {
                            try {
                                // Cek apakah siswa sudah ada
                                $stmt = $pdo->prepare("SELECT id_siswa FROM siswa WHERE nama_siswa = ? AND id_kelas = ?");
                                $stmt->execute([$nama_siswa, $kelas['id_kelas']]);
                                $existing_siswa = $stmt->fetch(PDO::FETCH_ASSOC);
                                
                                if (!$existing_siswa) {
                                    // Insert siswa baru
                                    $stmt = $pdo->prepare("INSERT INTO siswa (nama_siswa, id_kelas) VALUES (?, ?)");
                                    $stmt->execute([$nama_siswa, $kelas['id_kelas']]);
                                    $success_count++;
                                }
                            } catch (Exception $e) {
                                $error_count++;
                            }
                        } else {
                            $error_count++;
                        }
                    } else {
                        $error_count++;
                    }
                }
                fclose($handle);
                
                $message = "Import selesai! Berhasil: $success_count, Gagal: $error_count";
                $message_type = 'success';
            } else {
                $message = "Gagal membuka file CSV";
                $message_type = 'danger';
            }
        } else {
            $message = "Format file tidak didukung. Gunakan file CSV";
            $message_type = 'danger';
        }
    } else {
        $message = "Terjadi kesalahan saat upload file";
        $message_type = 'danger';
    }
}
?>

<h2>Import Data Siswa</h2>

<?php if ($message): ?>
    <div class="alert alert-<?= $message_type ?>"><?= $message ?></div>
<?php endif; ?>

<div class="card mb-4">
    <div class="card-header">
        <h5>Panduan Import</h5>
    </div>
    <div class="card-body">
        <p><strong>Format File CSV:</strong></p>
        <ul>
            <li>Kolom 1: Nama Siswa</li>
            <li>Kolom 2: Nama Kelas</li>
        </ul>
        <p><strong>Contoh Format:</strong></p>
        <pre>
Ahmad Fauzi,KPP A
Budi Santoso,KPP B
Citra Dewi,X-1
        </pre>
        <p><strong>Kelas yang tersedia:</strong></p>
        <div class="row">
            <?php 
            $kelas_list = getKelas($pdo);
            foreach($kelas_list as $kelas): ?>
                <div class="col-md-3">
                    <span class="badge bg-secondary"><?= $kelas['nama_kelas'] ?></span>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
</div>

<form method="POST" enctype="multipart/form-data">
    <div class="mb-3">
        <label class="form-label">Pilih File CSV</label>
        <input type="file" class="form-control" name="file_excel" accept=".csv" required>
        <div class="form-text">File harus berformat CSV</div>
    </div>
    
    <button type="submit" class="btn btn-primary">Import Data</button>
    <a href="daftar_siswa.php" class="btn btn-secondary">Lihat Daftar Siswa</a>
    <a href="../index.php" class="btn btn-outline-secondary">Kembali</a>
</form>

<div class="mt-4">
    <h5>Template CSV</h5>
    <p>Anda bisa download template CSV berikut:</p>
    <a href="download_template.php" class="btn btn-info">Download Template CSV</a>
</div>

<?php include_once '../includes/footer.php'; ?>