<?php 
include_once '../includes/header.php';
include_once '../includes/functions.php';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $tanggal = $_POST['tanggal'];
    $id_kelas = $_POST['id_kelas'];
    $id_siswa = $_POST['id_siswa'];
    $nama_barang1 = $_POST['nama_barang1'];
    $jumlah_barang1 = $_POST['jumlah_barang1'];
    $nama_barang2 = $_POST['nama_barang2'];
    $jumlah_barang2 = $_POST['jumlah_barang2'];
    
    $sql = "INSERT INTO pengambilan_atk (tanggal, id_kelas, id_siswa, nama_barang1, jumlah_barang1, nama_barang2, jumlah_barang2) 
            VALUES (?, ?, ?, ?, ?, ?, ?)";
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$tanggal, $id_kelas, $id_siswa, $nama_barang1, $jumlah_barang1, $nama_barang2, $jumlah_barang2]);
    
    echo "<div class='alert alert-success'>Pengambilan ATK berhasil ditambahkan!</div>";
}
?>

<h2>Tambah Pengambilan ATK</h2>

<form method="POST">
    <div class="mb-3">
        <label class="form-label">Tanggal</label>
        <input type="date" class="form-control" name="tanggal" value="<?= date('Y-m-d') ?>" required>
    </div>
    
    <div class="mb-3">
        <label class="form-label">Kelas</label>
        <select class="form-select" name="id_kelas" id="kelas" required>
            <option value="">Pilih Kelas</option>
            <?php foreach(getKelas($pdo) as $kelas): ?>
                <option value="<?= $kelas['id_kelas'] ?>"><?= $kelas['nama_kelas'] ?></option>
            <?php endforeach; ?>
        </select>
    </div>
    
    <div class="mb-3">
        <label class="form-label">Nama Siswa</label>
        <select class="form-select" name="id_siswa" id="nama_siswa" required>
            <option value="">Pilih Nama Siswa</option>
        </select>
    </div>
    
    <div class="row">
        <div class="col-md-6">
            <div class="mb-3">
                <label class="form-label">Nama Barang 1</label>
                <select class="form-select" name="nama_barang1" required>
                    <option value="">Pilih Barang</option>
                    <?php foreach(getATK($pdo) as $atk): ?>
                        <option value="<?= $atk['nama_atk'] ?>"><?= $atk['nama_atk'] ?></option>
                    <?php endforeach; ?>
                </select>
            </div>
            <div class="mb-3">
                <label class="form-label">Jumlah Barang 1</label>
                <input type="number" class="form-control" name="jumlah_barang1" min="1" required>
            </div>
        </div>
        <div class="col-md-6">
            <div class="mb-3">
                <label class="form-label">Nama Barang 2</label>
                <select class="form-select" name="nama_barang2">
                    <option value="">Pilih Barang (Opsional)</option>
                    <?php foreach(getATK($pdo) as $atk): ?>
                        <option value="<?= $atk['nama_atk'] ?>"><?= $atk['nama_atk'] ?></option>
                    <?php endforeach; ?>
                </select>
            </div>
            <div class="mb-3">
                <label class="form-label">Jumlah Barang 2</label>
                <input type="number" class="form-control" name="jumlah_barang2" min="0" value="0">
            </div>
        </div>
    </div>
    
    <button type="submit" class="btn btn-primary">Simpan</button>
    <a href="index.php" class="btn btn-secondary">Kembali</a>
</form>

<script>
document.getElementById('kelas').addEventListener('change', function() {
    var id_kelas = this.value;
    var namaSiswaSelect = document.getElementById('nama_siswa');
    
    if(id_kelas) {
        fetch('../kelas/get_nama.php?id_kelas=' + id_kelas)
            .then(response => response.json())
            .then(data => {
                namaSiswaSelect.innerHTML = '<option value="">Pilih Nama Siswa</option>';
                data.forEach(function(siswa) {
                    namaSiswaSelect.innerHTML += '<option value="' + siswa.id_siswa + '">' + siswa.nama_siswa + '</option>';
                });
            });
    } else {
        namaSiswaSelect.innerHTML = '<option value="">Pilih Nama Siswa</option>';
    }
});
</script>

<?php include_once '../includes/footer.php'; ?>