<?php 
include_once '../includes/header.php';
include_once '../includes/functions.php';

$id_pinjam = $_GET['id'] ?? 0;

// Get peminjaman data
$stmt = $pdo->prepare("SELECT pb.*, k.nama_kelas, s.nama_siswa, b.nama_barang 
                       FROM peminjaman_barang pb
                       JOIN kelas k ON pb.id_kelas = k.id_kelas
                       JOIN siswa s ON pb.id_siswa = s.id_siswa
                       JOIN barang b ON pb.id_barang = b.id_barang
                       WHERE pb.id_pinjam = ?");
$stmt->execute([$id_pinjam]);
$peminjaman = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$peminjaman) {
    header('Location: data_peminjaman.php');
    exit;
}

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $tanggal_kembali = $_POST['tanggal_kembali'];
    
    try {
        $sql = "UPDATE peminjaman_barang SET tanggal_kembali = ? WHERE id_pinjam = ?";
        $stmt = $pdo->prepare($sql);
        $stmt->execute([$tanggal_kembali, $id_pinjam]);
        
        $success_message = "Tanggal kembali berhasil diupdate!";
        // Refresh data
        $stmt = $pdo->prepare("SELECT pb.*, k.nama_kelas, s.nama_siswa, b.nama_barang 
                               FROM peminjaman_barang pb
                               JOIN kelas k ON pb.id_kelas = k.id_kelas
                               JOIN siswa s ON pb.id_siswa = s.id_siswa
                               JOIN barang b ON pb.id_barang = b.id_barang
                               WHERE pb.id_pinjam = ?");
        $stmt->execute([$id_pinjam]);
        $peminjaman = $stmt->fetch(PDO::FETCH_ASSOC);
    } catch(PDOException $e) {
        $error_message = "Error: " . $e->getMessage();
    }
}
?>

<h2>Edit Tanggal Kembali</h2>

<?php if(isset($success_message)): ?>
    <div class="alert alert-success"><?= $success_message ?></div>
<?php endif; ?>

<?php if(isset($error_message)): ?>
    <div class="alert alert-danger"><?= $error_message ?></div>
<?php endif; ?>

<div class="card mb-4">
    <div class="card-header">
        <h5>Detail Peminjaman</h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <p><strong>Nama Siswa:</strong> <?= $peminjaman['nama_siswa'] ?></p>
                <p><strong>Kelas:</strong> <?= $peminjaman['nama_kelas'] ?></p>
                <p><strong>Barang:</strong> <?= $peminjaman['nama_barang'] ?></p>
            </div>
            <div class="col-md-6">
                <p><strong>Tanggal Pinjam:</strong> <?= date('d-m-Y', strtotime($peminjaman['tanggal_pinjam'])) ?></p>
                <p><strong>Status:</strong> 
                    <?php if($peminjaman['status'] == 'dipinjam'): ?>
                        <span class="badge bg-warning">Dipinjam</span>
                    <?php else: ?>
                        <span class="badge bg-success">Dikembalikan</span>
                    <?php endif; ?>
                </p>
            </div>
        </div>
    </div>
</div>

<form method="POST">
    <div class="mb-3">
        <label class="form-label">Tanggal Kembali</label>
        <input type="date" class="form-control" name="tanggal_kembali" 
               value="<?= $peminjaman['tanggal_kembali'] ?: date('Y-m-d') ?>" required>
    </div>
    
    <button type="submit" class="btn btn-primary">Update Tanggal Kembali</button>
    <a href="data_peminjaman.php" class="btn btn-secondary">Batal</a>
</form>

<?php include_once '../includes/footer.php'; ?>