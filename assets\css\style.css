body {
    background-color: #f8f9fa;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.container {
    flex: 1;
}

.card {
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,.1);
    transition: transform 0.2s ease-in-out;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,.15);
}

.navbar-brand {
    font-weight: bold;
}

.table th {
    background-color: #e9ecef;
    font-weight: 600;
}

.table-striped > tbody > tr:nth-of-type(odd) > td {
    background-color: rgba(0,0,0,.02);
}

.badge {
    font-size: 0.8em;
}

.btn {
    border-radius: 6px;
}

.alert {
    border-radius: 8px;
}

footer {
    margin-top: auto;
}

/* Responsive improvements */
@media (max-width: 768px) {
    .card-body .btn {
        font-size: 0.9rem;
    }

    .table-responsive {
        font-size: 0.9rem;
    }
}