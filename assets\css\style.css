/* Modern CSS Variables for consistent theming */
:root {
    --primary-color: #6366f1;
    --primary-dark: #4f46e5;
    --secondary-color: #64748b;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --info-color: #06b6d4;
    --light-color: #f8fafc;
    --dark-color: #1e293b;
    --border-radius: 12px;
    --border-radius-sm: 8px;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-success: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* Modern body styling */
body {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
}

.container {
    flex: 1;
}

/* Modern Card Design */
.card {
    margin-bottom: 1.5rem;
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    overflow: hidden;
}

.card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.card-header {
    background: var(--gradient-primary);
    color: white;
    border: none;
    padding: 1rem 1.25rem;
    font-weight: 600;
    font-size: 1rem;
}

.card-body {
    padding: 1.5rem;
}

/* Modern Button Styles */
.btn {
    border-radius: var(--border-radius-sm);
    font-weight: 500;
    padding: 0.75rem 1.5rem;
    transition: all 0.2s ease-in-out;
    border: none;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: var(--gradient-primary);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.btn-success {
    background: linear-gradient(135deg, var(--success-color) 0%, #059669 100%);
    color: white;
}

.btn-info {
    background: linear-gradient(135deg, var(--info-color) 0%, #0891b2 100%);
    color: white;
}

.btn-warning {
    background: linear-gradient(135deg, var(--warning-color) 0%, #d97706 100%);
    color: white;
}

.btn-outline-secondary {
    border: 2px solid var(--secondary-color);
    color: var(--secondary-color);
    background: transparent;
}

.btn-outline-secondary:hover {
    background: var(--secondary-color);
    color: white;
    transform: translateY(-2px);
}

/* Modern Navbar */
.navbar {
    background: var(--gradient-primary) !important;
    backdrop-filter: blur(10px);
    box-shadow: var(--shadow-md);
    padding: 1rem 0;
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
    color: white !important;
}

.navbar-nav .nav-link {
    color: rgba(255, 255, 255, 0.9) !important;
    font-weight: 500;
    margin: 0 0.5rem;
    padding: 0.5rem 1rem !important;
    border-radius: var(--border-radius-sm);
    transition: all 0.2s ease;
}

.navbar-nav .nav-link:hover {
    background: rgba(255, 255, 255, 0.1);
    color: white !important;
    transform: translateY(-1px);
}

/* Modern Table Styles */
.table {
    background: white;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.table th {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    font-weight: 600;
    color: var(--dark-color);
    border: none;
    padding: 1rem;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.table td {
    padding: 1rem;
    border: none;
    border-bottom: 1px solid #f1f5f9;
    vertical-align: middle;
}

.table-striped > tbody > tr:nth-of-type(odd) > td {
    background-color: #f8fafc;
}

.table tbody tr:hover {
    background-color: #f1f5f9;
    transform: scale(1.01);
    transition: all 0.2s ease;
}

/* Modern Badge Styles */
.badge {
    font-size: 0.75rem;
    font-weight: 500;
    padding: 0.5rem 0.75rem;
    border-radius: 50px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.bg-warning {
    background: linear-gradient(135deg, var(--warning-color) 0%, #d97706 100%) !important;
}

.bg-success {
    background: linear-gradient(135deg, var(--success-color) 0%, #059669 100%) !important;
}

.bg-danger {
    background: linear-gradient(135deg, var(--danger-color) 0%, #dc2626 100%) !important;
}

/* Modern Alert Styles */
.alert {
    border-radius: var(--border-radius);
    border: none;
    padding: 1rem 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: var(--shadow-sm);
}

.alert-success {
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(5, 150, 105, 0.1) 100%);
    color: #065f46;
    border-left: 4px solid var(--success-color);
}

.alert-danger {
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.1) 0%, rgba(220, 38, 38, 0.1) 100%);
    color: #991b1b;
    border-left: 4px solid var(--danger-color);
}

/* Modern Hero Section */
.jumbotron {
    background: var(--gradient-primary);
    border-radius: 0 0 2rem 2rem;
    position: relative;
    overflow: hidden;
}

.jumbotron::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 100" fill="rgba(255,255,255,0.1)"><polygon points="1000,100 1000,0 0,100"/></svg>');
    background-size: cover;
}

.display-4 {
    font-weight: 700;
    margin-bottom: 1rem;
}

/* Modern Footer */
footer {
    margin-top: auto;
    background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
    color: white;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Modern Form Styles */
.form-control {
    border-radius: var(--border-radius-sm);
    border: 2px solid #e2e8f0;
    padding: 0.75rem 1rem;
    transition: all 0.2s ease;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.form-label {
    font-weight: 500;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

/* Responsive Design - Mobile First */
@media (max-width: 576px) {
    .container {
        padding: 0 1rem;
    }

    .card {
        margin-bottom: 1rem;
        border-radius: var(--border-radius-sm);
    }

    .card-body {
        padding: 1rem;
    }

    .btn {
        padding: 0.625rem 1.25rem;
        font-size: 0.9rem;
        width: 100%;
        margin-bottom: 0.5rem;
    }

    .display-4 {
        font-size: 2rem;
    }

    .lead {
        font-size: 1rem;
    }

    .jumbotron {
        padding: 2rem 0 !important;
        border-radius: 0 0 1rem 1rem;
    }

    .table-responsive {
        font-size: 0.85rem;
        border-radius: var(--border-radius-sm);
    }

    .table th,
    .table td {
        padding: 0.75rem 0.5rem;
    }

    .navbar-brand {
        font-size: 1.25rem;
    }

    .navbar-nav {
        margin-top: 1rem;
    }

    .navbar-nav .nav-link {
        margin: 0.25rem 0;
        text-align: center;
    }
}

@media (min-width: 577px) and (max-width: 768px) {
    .col-md-3 {
        flex: 0 0 50%;
        max-width: 50%;
    }

    .btn {
        font-size: 0.9rem;
    }

    .display-4 {
        font-size: 2.5rem;
    }

    .table-responsive {
        font-size: 0.9rem;
    }
}

@media (min-width: 769px) and (max-width: 992px) {
    .col-md-3 {
        flex: 0 0 50%;
        max-width: 50%;
    }

    .container {
        max-width: 720px;
    }
}

@media (min-width: 993px) {
    .container {
        max-width: 1140px;
    }

    .card-body .btn {
        font-size: 1rem;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    :root {
        --light-color: #1e293b;
        --dark-color: #f8fafc;
    }

    body {
        background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
        color: #f8fafc;
    }

    .card {
        background: rgba(30, 41, 59, 0.95);
        color: #f8fafc;
    }

    .table {
        background: #1e293b;
        color: #f8fafc;
    }

    .table th {
        background: linear-gradient(135deg, #334155 0%, #475569 100%);
        color: #f8fafc;
    }

    .table td {
        border-bottom-color: #334155;
    }

    .table-striped > tbody > tr:nth-of-type(odd) > td {
        background-color: #334155;
    }
}

/* Print styles */
@media print {
    .navbar,
    .btn,
    footer {
        display: none !important;
    }

    .card {
        box-shadow: none;
        border: 1px solid #ddd;
    }

    .table {
        font-size: 12px;
    }

    body {
        background: white !important;
        color: black !important;
    }
}

/* Additional Modern Animations */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

/* Smooth scroll behavior */
html {
    scroll-behavior: smooth;
}

/* Enhanced card animations */
.card {
    animation: slideInUp 0.6s ease-out;
}

.card:nth-child(1) { animation-delay: 0.1s; }
.card:nth-child(2) { animation-delay: 0.2s; }
.card:nth-child(3) { animation-delay: 0.3s; }
.card:nth-child(4) { animation-delay: 0.4s; }

/* Breadcrumb styling */
.breadcrumb {
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius-sm);
    padding: 0.5rem 1rem;
}

.breadcrumb-item + .breadcrumb-item::before {
    content: "›";
    color: var(--secondary-color);
}

.breadcrumb-item a {
    color: var(--primary-color);
    text-decoration: none;
}

.breadcrumb-item a:hover {
    text-decoration: underline;
}

/* Enhanced button group */
.btn-group .btn {
    position: relative;
    z-index: 1;
}

.btn-group .btn:hover {
    z-index: 2;
}

/* Loading states */
.btn.loading {
    pointer-events: none;
    opacity: 0.7;
}

/* Enhanced table hover */
.table tbody tr {
    transition: all 0.2s ease;
}

.table tbody tr:hover {
    background-color: rgba(99, 102, 241, 0.05) !important;
    transform: translateX(5px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Modern scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--primary-dark);
}

/* Focus states */
.btn:focus,
.form-control:focus,
.form-select:focus {
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.2);
    outline: none;
}

/* Enhanced navbar active state */
.navbar-nav .nav-link.active {
    background: rgba(255, 255, 255, 0.2);
    border-radius: var(--border-radius-sm);
    font-weight: 600;
}

/* Utility classes */
.text-gradient {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.shadow-soft {
    box-shadow: 0 2px 25px rgba(0, 0, 0, 0.1);
}

.border-gradient {
    border: 2px solid transparent;
    background: linear-gradient(white, white) padding-box,
                var(--gradient-primary) border-box;
}