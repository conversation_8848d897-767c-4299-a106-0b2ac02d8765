<?php
include_once '../config/database.php';

function getKelas($pdo) {
    $stmt = $pdo->query("SELECT * FROM kelas ORDER BY nama_kelas");
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

function getNamaSiswa($pdo, $id_kelas) {
    $stmt = $pdo->prepare("SELECT * FROM siswa WHERE id_kelas = ? ORDER BY nama_siswa");
    $stmt->execute([$id_kelas]);
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

function getBarang($pdo) {
    $stmt = $pdo->query("SELECT * FROM barang ORDER BY nama_barang");
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

function getATK($pdo) {
    $stmt = $pdo->query("SELECT * FROM atk ORDER BY nama_atk");
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}
?>