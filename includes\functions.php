<?php
// Determine the correct path to database config
$current_dir = basename(dirname($_SERVER['PHP_SELF']));
if ($current_dir == 'aplikasi_peminjaman') {
    include_once 'config/database.php';
} else {
    include_once '../config/database.php';
}

function getKelas($pdo) {
    $stmt = $pdo->query("SELECT * FROM kelas ORDER BY nama_kelas");
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

function getNamaSiswa($pdo, $id_kelas) {
    $stmt = $pdo->prepare("SELECT * FROM siswa WHERE id_kelas = ? ORDER BY nama_siswa");
    $stmt->execute([$id_kelas]);
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

function getBarang($pdo) {
    $stmt = $pdo->query("SELECT * FROM barang ORDER BY nama_barang");
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

function getATK($pdo) {
    $stmt = $pdo->query("SELECT * FROM atk ORDER BY nama_atk");
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

// Fungsi untuk statistik peminjaman barang
function getStatistikPeminjaman($pdo) {
    try {
        // Total peminjaman
        $stmt = $pdo->query("SELECT COUNT(*) as total FROM peminjaman_barang");
        $total = $stmt->fetch(PDO::FETCH_ASSOC)['total'];

        // Sedang dipinjam
        $stmt = $pdo->query("SELECT COUNT(*) as sedang_dipinjam FROM peminjaman_barang WHERE status = 'dipinjam'");
        $sedang_dipinjam = $stmt->fetch(PDO::FETCH_ASSOC)['sedang_dipinjam'];

        // Sudah dikembalikan
        $stmt = $pdo->query("SELECT COUNT(*) as dikembalikan FROM peminjaman_barang WHERE status = 'dikembalikan'");
        $dikembalikan = $stmt->fetch(PDO::FETCH_ASSOC)['dikembalikan'];

        // Terlambat (tanggal kembali sudah lewat tapi status masih dipinjam)
        $stmt = $pdo->query("SELECT COUNT(*) as terlambat FROM peminjaman_barang WHERE status = 'dipinjam' AND tanggal_kembali < CURDATE()");
        $terlambat = $stmt->fetch(PDO::FETCH_ASSOC)['terlambat'];

        return [
            'total' => $total,
            'sedang_dipinjam' => $sedang_dipinjam,
            'dikembalikan' => $dikembalikan,
            'terlambat' => $terlambat
        ];
    } catch(PDOException $e) {
        return [
            'total' => 0,
            'sedang_dipinjam' => 0,
            'dikembalikan' => 0,
            'terlambat' => 0
        ];
    }
}

// Fungsi untuk statistik pengambilan ATK
function getStatistikATK($pdo) {
    try {
        // Total pengambilan
        $stmt = $pdo->query("SELECT COUNT(*) as total FROM pengambilan_atk");
        $total = $stmt->fetch(PDO::FETCH_ASSOC)['total'];

        // Pengambilan bulan ini (gunakan kolom 'tanggal' bukan 'tanggal_ambil')
        $stmt = $pdo->query("SELECT COUNT(*) as bulan_ini FROM pengambilan_atk WHERE MONTH(tanggal) = MONTH(CURDATE()) AND YEAR(tanggal) = YEAR(CURDATE())");
        $bulan_ini = $stmt->fetch(PDO::FETCH_ASSOC)['bulan_ini'];

        // Jenis ATK (hitung dari tabel atk)
        $stmt = $pdo->query("SELECT COUNT(*) as jenis_atk FROM atk");
        $jenis_atk = $stmt->fetch(PDO::FETCH_ASSOC)['jenis_atk'];

        // Stok menipis (kurang dari 10)
        $stmt = $pdo->query("SELECT COUNT(*) as stok_menipis FROM atk WHERE stok < 10");
        $stok_menipis = $stmt->fetch(PDO::FETCH_ASSOC)['stok_menipis'];

        return [
            'total' => $total,
            'bulan_ini' => $bulan_ini,
            'jenis_atk' => $jenis_atk,
            'stok_menipis' => $stok_menipis
        ];
    } catch(PDOException $e) {
        // Debug: tampilkan error jika dalam mode development
        error_log("Error getStatistikATK: " . $e->getMessage());
        return [
            'total' => 0,
            'bulan_ini' => 0,
            'jenis_atk' => 0,
            'stok_menipis' => 0
        ];
    }
}

// Fungsi untuk format tanggal Indonesia
function formatTanggalIndonesia($tanggal) {
    $bulan = [
        1 => 'Januari', 'Februari', 'Maret', 'April', 'Mei', 'Juni',
        'Juli', 'Agustus', 'September', 'Oktober', 'November', 'Desember'
    ];

    $timestamp = strtotime($tanggal);
    $hari = date('d', $timestamp);
    $bulan_num = date('n', $timestamp);
    $tahun = date('Y', $timestamp);

    return $hari . ' ' . $bulan[$bulan_num] . ' ' . $tahun;
}

// Fungsi untuk cek koneksi database
function cekKoneksiDatabase($pdo) {
    try {
        $stmt = $pdo->query("SELECT 1");
        return true;
    } catch(PDOException $e) {
        return false;
    }
}
?>