<?php
include_once '../config/database.php';

// Set headers untuk download CSV
header('Content-Type: text/csv');
header('Content-Disposition: attachment; filename="data_siswa_' . date('Y-m-d') . '.csv"');

// Query data siswa
$sql = "SELECT s.nama_siswa, k.nama_kelas 
        FROM siswa s 
        JOIN kelas k ON s.id_kelas = k.id_kelas 
        ORDER BY k.nama_kelas, s.nama_siswa";
$stmt = $pdo->query($sql);
$siswas = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Output CSV
$output = fopen('php://output', 'w');

// Header CSV
fputcsv($output, ['Nama Siswa', 'Nama Kelas']);

// Data siswa
foreach($siswas as $siswa) {
    fputcsv($output, [$siswa['nama_siswa'], $siswa['nama_kelas']]);
}

fclose($output);
exit;
?>