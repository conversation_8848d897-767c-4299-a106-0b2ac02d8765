<?php 
include_once '../includes/header.php';
include_once '../includes/functions.php';

$id_siswa = $_GET['id'] ?? 0;

// Get student data
$stmt = $pdo->prepare("SELECT * FROM siswa WHERE id_siswa = ?");
$stmt->execute([$id_siswa]);
$siswa = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$siswa) {
    header('Location: daftar_siswa.php');
    exit;
}

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $nama_siswa = $_POST['nama_siswa'];
    $id_kelas = $_POST['id_kelas'];
    
    try {
        $sql = "UPDATE siswa SET nama_siswa = ?, id_kelas = ? WHERE id_siswa = ?";
        $stmt = $pdo->prepare($sql);
        $stmt->execute([$nama_siswa, $id_kelas, $id_siswa]);
        
        $success_message = "Data siswa berhasil diupdate!";
        // Refresh data
        $stmt = $pdo->prepare("SELECT * FROM siswa WHERE id_siswa = ?");
        $stmt->execute([$id_siswa]);
        $siswa = $stmt->fetch(PDO::FETCH_ASSOC);
    } catch(PDOException $e) {
        $error_message = "Error: " . $e->getMessage();
    }
}
?>

<h2>Edit Siswa</h2>

<?php if(isset($success_message)): ?>
    <div class="alert alert-success"><?= $success_message ?></div>
<?php endif; ?>

<?php if(isset($error_message)): ?>
    <div class="alert alert-danger"><?= $error_message ?></div>
<?php endif; ?>

<form method="POST">
    <div class="mb-3">
        <label class="form-label">Nama Siswa</label>
        <input type="text" class="form-control" name="nama_siswa" 
               value="<?= $siswa['nama_siswa'] ?>" required>
    </div>
    
    <div class="mb-3">
        <label class="form-label">Kelas</label>
        <select class="form-select" name="id_kelas" required>
            <option value="">Pilih Kelas</option>
            <?php foreach(getKelas($pdo) as $kelas): ?>
                <option value="<?= $kelas['id_kelas'] ?>" 
                        <?= ($kelas['id_kelas'] == $siswa['id_kelas']) ? 'selected' : '' ?>>
                    <?= $kelas['nama_kelas'] ?>
                </option>
            <?php endforeach; ?>
        </select>
    </div>
    
    <button type="submit" class="btn btn-primary">Update Siswa</button>
    <a href="daftar_siswa.php" class="btn btn-secondary">Batal</a>
</form>

<?php include_once '../includes/footer.php'; ?>